# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
.next/
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log

# Coverage directory used by tools like istanbul
coverage/

# Prisma generated files
prisma/generated/

# Database files
*.db
*.sqlite

# OS generated files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/

# Temporary files
*.tmp
*.temp

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
*.generated.*

# Config files that don't need linting
*.config.js
*.config.mjs
*.config.ts
tailwind.config.js
postcss.config.js
next.config.js
next.config.mjs

# Test coverage
.nyc_output

# Storybook
.storybook/
storybook-static/
