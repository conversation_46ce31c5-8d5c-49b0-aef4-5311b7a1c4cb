# TECHNOLOWAY-WEB

A modern, full-stack web application built with Next.js, TypeScript, Tailwind CSS, and Prisma.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Set up database
npm run db:setup

# Start development server
npm run dev
```

## 📁 Project Structure

```
TECHNOLOWAY-WEB/
├── 📁 app/                          # Next.js App Router (pages & routes)
├── 📁 src/
│   ├── 📁 components/               # Reusable UI components
│   ├── 📁 services/                 # Business logic & external services
│   │   ├── 📁 api/                  # API service functions
│   │   ├── 📁 auth/                 # Authentication services
│   │   ├── 📁 payment/              # Payment services
│   │   ├── 📁 email/                # Email services
│   │   ├── 📁 file-upload/          # File upload services
│   │   ├── 📁 content/              # Content management services
│   │   └── 📁 chat/                 # Chat services
│   ├── 📁 lib/                      # Utilities & helpers
│   ├── 📁 types/                    # TypeScript type definitions
│   ├── 📁 hooks/                    # Custom React hooks
│   ├── 📁 data/                     # Static data & constants
│   └── 📁 config/                   # Configuration files
├── 📁 prisma/                       # Database schema & migrations
├── 📁 scripts/                      # Build & utility scripts
├── 📁 public/                       # Static assets
├── 📁 tests/                        # Test files
└── 📁 docs/                         # Documentation
```

## 🛠️ Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run dev:turbo        # Start with Turbo mode
npm run build            # Build for production
npm run start            # Start production server

# Testing
npm run test             # Run tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage

# Database
npm run db:generate      # Generate Prisma client
npm run db:push          # Push schema to database
npm run db:migrate       # Run database migrations
npm run db:studio        # Open Prisma Studio
npm run db:seed          # Seed database
npm run db:reset         # Reset and seed database

# Code Quality
npm run lint             # Run ESLint
npm run type-check       # Run TypeScript type checking
```

## 📚 Documentation

- [Project Structure](./docs/PROJECT-STRUCTURE.md) - Detailed structure documentation
- [Migration Guide](./docs/MIGRATION-GUIDE.md) - Import statement updates
- [Reorganization Summary](./docs/REORGANIZATION-SUMMARY.md) - What was changed

## 🔧 Key Features

- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Prisma** for database management
- **NextAuth.js** for authentication
- **Stripe** for payments
- **File upload** functionality
- **Real-time chat** system
- **Admin dashboard** with CRUD operations
- **Client dashboard** for customers
- **Email notifications**
- **Analytics and monitoring**

## 🏗️ Architecture

### Services Layer
The application uses a modular service architecture:

```typescript
// Import services
import { apiUtils } from '@/services/api/api-utils';
import { authConfig } from '@/services/auth/auth-config';
import { paymentUtils } from '@/services/payment/payment-utils';
import { emailService } from '@/services/email';
```

### Configuration
Centralized configuration management:

```typescript
// Import configuration
import { prisma } from '@/config/prisma';
import { analytics } from '@/config/analytics';
import { monitoring } from '@/config/monitoring';
```

### Utilities
Reusable utility functions:

```typescript
// Import utilities
import { formatDate, validateEmail } from '@/lib/utils';
import { useLocalStorage } from '@/lib/hooks';
```

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run specific test file
npm test -- chat-interface.test.tsx

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

1. **Environment Setup**
   ```bash
   # Set production environment variables
   cp .env.example .env.production
   ```

2. **Database Migration**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

3. **Build and Deploy**
   ```bash
   npm run build
   npm start
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the [documentation](./docs/)
- Review the [migration guide](./docs/MIGRATION-GUIDE.md)
- Open an issue on GitHub

---

**Built with ❤️ using modern web technologies** 