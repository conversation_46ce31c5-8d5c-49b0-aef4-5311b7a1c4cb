# Image Optimization Guide

## 🖼️ Current Status

✅ **Placeholder images created** - Your homepage now loads without 404 errors  
⚠️ **Production images needed** - Replace SVG placeholders with optimized images

## 📁 Required Images

### 1. Hero Background (`/public/images/hero-bg.webp`)
- **Size**: 1920x1080px
- **Format**: WebP (with AVIF fallback)
- **Target size**: ~200KB
- **Content**: Technology/coding themed background
- **Suggestions**:
  - Abstract tech patterns
  - Code editor screenshots
  - Modern office/workspace
  - Geometric tech designs

### 2. Client Logos (`/public/images/clients/`)
- **Size**: 120x60px (2:1 aspect ratio)
- **Format**: WebP or SVG
- **Target size**: ~5KB each
- **Files needed**:
  - `microsoft.webp`
  - `google.webp`
  - `amazon.webp`
  - `apple.webp`
  - `meta.webp`
  - `netflix.webp`

### 3. Open Graph Image (`/public/images/og-homepage.jpg`)
- **Size**: 1200x630px
- **Format**: JPG
- **Target size**: ~100KB
- **Content**: Branded social sharing image

## 🛠️ Optimization Tools

### Online Tools (Recommended)
1. **[Squoosh.app](https://squoosh.app/)** - Google's image optimizer
   - Drag & drop interface
   - Real-time comparison
   - Multiple format support

2. **[TinyPNG](https://tinypng.com/)** - PNG/JPG compression
   - Batch processing
   - API available

3. **[Convertio](https://convertio.co/)** - Format conversion
   - Supports WebP/AVIF
   - Batch conversion

### Command Line Tools
```bash
# Install Sharp CLI
npm install -g sharp-cli

# Convert to WebP
sharp input.jpg -o output.webp --webp-quality 85

# Resize and convert
sharp input.jpg -o output.webp --resize 1920 1080 --webp-quality 85
```

## 📋 Step-by-Step Instructions

### Step 1: Gather Source Images
1. **Hero Background**: Find a high-quality tech background (2000x1200px+)
2. **Client Logos**: Download official brand assets:
   - Microsoft: [Brand Portal](https://www.microsoft.com/en-us/legal/intellectualproperty/trademarks)
   - Google: [Brand Resource Center](https://about.google/brand-resource-center/)
   - Amazon: [Brand Guidelines](https://advertising.amazon.com/resources/ad-specs/brand-guidelines)
   - Apple: [Marketing Guidelines](https://developer.apple.com/app-store/marketing/guidelines/)
   - Meta: [Brand Resources](https://about.meta.com/brand/resources/)
   - Netflix: [Brand Assets](https://brand.netflix.com/)

### Step 2: Optimize Images
1. **Resize** to target dimensions
2. **Convert** to WebP format
3. **Compress** to target file sizes
4. **Test** in different browsers

### Step 3: Replace Placeholders
```bash
# Replace the SVG placeholders with optimized images
cp optimized-hero-bg.webp public/images/hero-bg.webp
cp optimized-microsoft.webp public/images/clients/microsoft.webp
# ... repeat for all images
```

### Step 4: Update Code (if needed)
If you change file extensions, update the references:

```typescript
// In src/app/page.tsx
const clientLogos = [
  { name: 'Microsoft', logo: '/images/clients/microsoft.webp', url: '#' },
  // ...
];

// In src/components/home/<USER>
<Image src="/images/hero-bg.webp" ... />
```

## 🎯 Performance Targets

### Before Optimization (Current SVG placeholders)
- Hero background: ~5KB (SVG)
- Client logos: ~1KB each (SVG)
- Total: ~11KB

### After Optimization (Target)
- Hero background: ~200KB (WebP)
- Client logos: ~5KB each (WebP)
- Total: ~230KB

### Performance Impact
- **LCP improvement**: Faster hero image loading
- **Visual stability**: No layout shifts
- **SEO boost**: Proper Open Graph images

## 🔧 Automation Script

Create a batch optimization script:

```javascript
// scripts/optimize-images.js
const sharp = require('sharp');
const fs = require('fs');

const optimizeImage = async (input, output, width, height, quality = 85) => {
  await sharp(input)
    .resize(width, height, { fit: 'cover' })
    .webp({ quality })
    .toFile(output);
  console.log(`✅ Optimized: ${output}`);
};

// Usage
optimizeImage('source/hero.jpg', 'public/images/hero-bg.webp', 1920, 1080);
optimizeImage('source/microsoft.png', 'public/images/clients/microsoft.webp', 120, 60);
```

## 📊 Quality Checklist

### ✅ Image Quality
- [ ] Hero background is sharp and professional
- [ ] Client logos are clear and recognizable
- [ ] Colors match brand guidelines
- [ ] No compression artifacts

### ✅ Performance
- [ ] File sizes meet targets
- [ ] WebP format used where supported
- [ ] Images load quickly on slow connections
- [ ] No layout shifts during loading

### ✅ Accessibility
- [ ] Alt text is descriptive
- [ ] Images work without JavaScript
- [ ] High contrast for readability
- [ ] Responsive on all devices

## 🚀 Quick Start

1. **Download this starter pack**: [Free stock images for tech websites]
2. **Use Squoosh.app** to optimize each image
3. **Replace the SVG files** in `/public/images/`
4. **Test the homepage** - images should load perfectly!

## 💡 Pro Tips

1. **Use WebP with JPG fallback** for maximum compatibility
2. **Implement lazy loading** for below-the-fold images
3. **Consider AVIF format** for even better compression
4. **Monitor Core Web Vitals** after image updates
5. **Use a CDN** for production deployments

## 🔄 Maintenance

- **Monthly**: Check for broken image links
- **Quarterly**: Re-optimize images with new tools
- **Annually**: Update client logos if brands change
- **As needed**: Replace hero background for freshness

---

**Need help?** The current SVG placeholders will work perfectly for development and testing. Replace them with optimized images when you're ready for production!
