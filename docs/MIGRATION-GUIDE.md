# Migration Guide - Import Statement Updates

## Overview

This guide helps you update import statements after the project structure reorganization.

## Import Path Changes

### Services

#### Before
```typescript
import { apiUtils } from '@/lib/api-utils';
import { authConfig } from '@/lib/auth-config';
import { paymentUtils } from '@/lib/payment-utils';
import { emailService } from '@/lib/email';
import { fileUpload } from '@/lib/file-upload';
import { chatAccess } from '@/lib/chat-access-control';
```

#### After
```typescript
import { apiUtils } from '@/services/api/api-utils';
import { authConfig } from '@/services/auth/auth-config';
import { paymentUtils } from '@/services/payment/payment-utils';
import { emailService } from '@/services/email';
import { fileUpload } from '@/services/file-upload/file-upload';
import { chatAccess } from '@/services/chat/chat-access-control';
```

### Configuration

#### Before
```typescript
import { prisma } from '@/lib/prisma';
import { analytics } from '@/lib/analytics';
import { monitoring } from '@/lib/monitoring';
```

#### After
```typescript
import { prisma } from '@/config/prisma';
import { analytics } from '@/config/analytics';
import { monitoring } from '@/config/monitoring';
```

### Utilities

#### Before
```typescript
import { dateUtils } from '@/lib/date-utils';
import { urlUtils } from '@/lib/url-utils';
import { validations } from '@/lib/validations';
```

#### After
```typescript
import { dateUtils } from '@/lib/utils/date-utils';
import { urlUtils } from '@/lib/utils/url-utils';
import { validations } from '@/lib/utils/validations';
```

### Types

#### Before
```typescript
import type { Technology } from '@/types/technology';
import type { NextAuthConfig } from '@/types/next-auth';
```

#### After
```typescript
import type { Technology } from '@/types/shared/technology';
import type { NextAuthConfig } from '@/types/auth/next-auth';
```

## Batch Update Commands

### Find and Replace Patterns

Use these patterns in your code editor's find and replace:

1. **API Utils**
   - Find: `@/lib/api-utils`
   - Replace: `@/services/api/api-utils`

2. **Auth Config**
   - Find: `@/lib/auth-config`
   - Replace: `@/services/auth/auth-config`

3. **Payment Utils**
   - Find: `@/lib/payment-utils`
   - Replace: `@/services/payment/payment-utils`

4. **Email Service**
   - Find: `@/lib/email`
   - Replace: `@/services/email`

5. **File Upload**
   - Find: `@/lib/file-upload`
   - Replace: `@/services/file-upload/file-upload`

6. **Chat Access**
   - Find: `@/lib/chat-access-control`
   - Replace: `@/services/chat/chat-access-control`

7. **Prisma**
   - Find: `@/lib/prisma`
   - Replace: `@/config/prisma`

8. **Analytics**
   - Find: `@/lib/analytics`
   - Replace: `@/config/analytics`

9. **Monitoring**
   - Find: `@/lib/monitoring`
   - Replace: `@/config/monitoring`

10. **Utilities**
    - Find: `@/lib/date-utils`
    - Replace: `@/lib/utils/date-utils`
    - Find: `@/lib/url-utils`
    - Replace: `@/lib/utils/url-utils`
    - Find: `@/lib/validations`
    - Replace: `@/lib/utils/validations`

11. **Types**
    - Find: `@/types/technology`
    - Replace: `@/types/shared/technology`
    - Find: `@/types/next-auth`
    - Replace: `@/types/auth/next-auth`

## Function Name Changes

### File Upload Functions

The following functions were renamed to avoid conflicts:

- `validateFileType` → `validateUploadFileType`
- `validateFileSize` → `validateUploadFileSize`

Update any imports or usage of these functions accordingly.

## Verification Steps

After updating imports:

1. **Run TypeScript Check**
   ```bash
   npm run type-check
   ```

2. **Run Linting**
   ```bash
   npm run lint
   ```

3. **Run Tests**
   ```bash
   npm run test
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

## Common Issues and Solutions

### 1. Module Not Found Errors
- Ensure the file path is correct
- Check that index files are properly exporting functions
- Verify the file was moved to the correct location

### 2. Function Name Conflicts
- Use the new function names (e.g., `validateUploadFileType`)
- Import specific functions instead of using wildcard imports

### 3. Type Errors
- Update type imports to use the new paths
- Ensure type definitions are properly exported

## Rollback Plan

If issues arise, you can:

1. **Revert the file moves** using git:
   ```bash
   git reset --hard HEAD~1
   ```

2. **Manually restore** the original structure
3. **Update imports** gradually instead of all at once

## Support

If you encounter issues during migration:

1. Check the console for specific error messages
2. Verify import paths are correct
3. Ensure all dependencies are properly installed
4. Review the project structure documentation
