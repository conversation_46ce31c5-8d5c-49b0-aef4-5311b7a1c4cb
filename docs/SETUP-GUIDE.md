# Technoloway Setup Guide

This guide will help you set up the migrated Technoloway application with PostgreSQL database, authentication, and file upload functionality.

## Prerequisites

Before starting, make sure you have:

- **Node.js 18+** installed
- **PostgreSQL** database server running
- **Git** for version control
- **Code editor** (VS Code recommended)

## Step 1: PostgreSQL Database Setup

### Option A: Local PostgreSQL Installation

1. **Install PostgreSQL** (if not already installed):
   - Windows: Download from https://www.postgresql.org/download/windows/
   - macOS: `brew install postgresql`
   - Linux: `sudo apt-get install postgresql postgresql-contrib`

2. **Start PostgreSQL service**:
   - Windows: Use Services app or `net start postgresql-x64-14`
   - macOS: `brew services start postgresql`
   - Linux: `sudo systemctl start postgresql`

3. **Create database**:
   ```bash
   # Connect to PostgreSQL
   psql -U postgres
   
   # Create database
   CREATE DATABASE technoloway_db;
   
   # Create user (optional)
   CREATE USER technoloway_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE technoloway_db TO technoloway_user;
   
   # Exit
   \q
   ```

### Option B: Cloud Database (Recommended for Production)

Use one of these cloud providers:
- **Supabase** (free tier available): https://supabase.com/
- **Railway** (free tier available): https://railway.app/
- **Neon** (free tier available): https://neon.tech/
- **AWS RDS**, **Google Cloud SQL**, or **Azure Database**

## Step 2: Environment Configuration

1. **Copy environment file**:
   ```bash
   cp .env.example .env.local
   ```

2. **Update `.env.local`** with your database credentials:
   ```env
   # Database - Replace with your actual connection string
   DATABASE_URL="postgresql://username:password@localhost:5432/technoloway_db"
   
   # NextAuth.js - Generate secure secrets for production
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secure-secret-here"
   
   # Application Settings
   APP_NAME="Technoloway"
   APP_URL="http://localhost:3000"
   ADMIN_EMAIL="<EMAIL>"
   
   # Security - Generate secure secrets for production
   JWT_SECRET="your-jwt-secret-here"
   
   # File Upload
   UPLOAD_DIR="./public/uploads"
   MAX_FILE_SIZE=10485760
   ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp,application/pdf"
   
   # Email (optional - for contact forms)
   SMTP_HOST="smtp.gmail.com"
   SMTP_PORT=587
   SMTP_USER="<EMAIL>"
   SMTP_PASS="your-app-password"
   ```

## Step 3: Database Setup

Run the automated database setup:

```bash
npm run db:setup
```

This will:
- Generate Prisma client
- Push database schema
- Seed with sample data

### Manual Setup (if automated setup fails):

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed database with sample data
npm run db:seed
```

## Step 4: Start Development Server

```bash
npm run dev
```

The application will be available at:
- **Website**: http://localhost:3000
- **Admin Dashboard**: http://localhost:3000/admin
- **API Documentation**: http://localhost:3000/api

## Step 5: Test Authentication

### Default Admin Credentials:
- **Email**: <EMAIL>
- **Password**: admin123

### Sign In:
1. Go to http://localhost:3000/auth/signin
2. Use the credentials above
3. You'll be redirected to the admin dashboard

## Step 6: Test File Upload

1. Go to the admin dashboard
2. Navigate to any section that supports file uploads
3. Try uploading images or PDF files
4. Files will be stored in `./public/uploads/`

## Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database Management
npm run db:setup        # Complete database setup
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema to database
npm run db:migrate      # Create and run migrations
npm run db:studio       # Open Prisma Studio (database GUI)
npm run db:seed         # Seed database with sample data
npm run db:reset        # Reset database and reseed

# Code Quality
npm run lint            # Run ESLint
```

## API Endpoints

### Public APIs:
- `GET /api/services` - List services
- `POST /api/contact` - Submit contact form
- `GET /api/blog` - List published blog posts

### Admin APIs (require authentication):
- **Services**: `/api/services` - Full CRUD operations
- **Projects**: `/api/projects` - Full CRUD operations
- **Clients**: `/api/clients` - Full CRUD operations
- **Blog**: `/api/blog` - Full CRUD operations
- **Team**: `/api/team` - Full CRUD operations
- **Technologies**: `/api/technologies` - Full CRUD operations
- **Upload**: `/api/upload` - File upload
- **Dashboard**: `/api/dashboard/stats` - Statistics

## File Upload Configuration

### Supported File Types:
- **Images**: JPEG, PNG, GIF, WebP
- **Documents**: PDF, Word documents

### Upload Limits:
- **Max file size**: 10MB (configurable)
- **Multiple files**: Supported
- **Image processing**: Automatic resize and optimization

### Upload Categories:
- `general` - General uploads
- `profile` - Profile pictures
- `project` - Project images
- `blog` - Blog post images
- `document` - Documents
- `logo` - Company logos

## Security Features

### Authentication:
- **NextAuth.js** with credentials provider
- **Secure password hashing** with bcrypt
- **JWT tokens** for session management
- **Role-based access control** (Admin, User, Client)

### File Upload Security:
- **File type validation**
- **File size limits**
- **Secure file naming**
- **Image processing** and optimization

### API Security:
- **Input validation** with Zod schemas
- **SQL injection protection** with Prisma
- **XSS protection** with React
- **CSRF protection** with Next.js

## Troubleshooting

### Database Connection Issues:
1. **Check PostgreSQL is running**:
   ```bash
   # Check if PostgreSQL is running
   pg_isready -h localhost -p 5432
   ```

2. **Verify database exists**:
   ```bash
   psql -U postgres -l
   ```

3. **Test connection string**:
   ```bash
   psql "postgresql://username:password@localhost:5432/technoloway_db"
   ```

### Common Issues:

**"Can't reach database server"**:
- Check PostgreSQL is running
- Verify connection string in `.env.local`
- Check firewall settings

**"Authentication failed"**:
- Verify username and password
- Check user permissions

**"Database does not exist"**:
- Create the database manually
- Run `npm run db:setup` again

**File upload not working**:
- Check `UPLOAD_DIR` exists and is writable
- Verify file size and type limits
- Check server disk space

### Getting Help:

1. **Check logs** in the terminal
2. **Use Prisma Studio** to inspect database: `npm run db:studio`
3. **Check API responses** in browser developer tools
4. **Review error messages** in the console

## Production Deployment

### Environment Variables:
- Generate secure secrets for `NEXTAUTH_SECRET` and `JWT_SECRET`
- Use production database URL
- Set `NODE_ENV=production`
- Configure email settings for notifications

### Recommended Platforms:
- **Vercel** (recommended for Next.js)
- **Netlify**
- **Railway**
- **AWS**, **Google Cloud**, **Azure**

### Database:
- Use managed PostgreSQL service
- Enable SSL connections
- Set up regular backups
- Monitor performance

## Next Steps

1. **Customize branding** and content
2. **Set up email notifications**
3. **Configure analytics** (Google Analytics)
4. **Add more authentication providers** (Google, GitHub)
5. **Implement advanced features** (real-time chat, payments)
6. **Set up monitoring** and error tracking
7. **Configure CI/CD pipeline**
8. **Add comprehensive testing**

## Support

For additional help:
- Check the `MIGRATION-GUIDE.md` for detailed technical information
- Review the `MIGRATION-SUMMARY.md` for an overview
- Check Next.js documentation: https://nextjs.org/docs
- Check Prisma documentation: https://www.prisma.io/docs
