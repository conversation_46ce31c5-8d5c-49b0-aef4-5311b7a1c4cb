import fs from 'fs/promises'
import path from 'path'

export interface FileUpdateRequest {
  filePath: string
  lineNumber: number
  oldText: string
  newText: string
}

export class FileUpdater {
  /**
   * Updates a specific line in a file with new text
   */
  static async updateLine(request: FileUpdateRequest): Promise<{ success: boolean; message: string }> {
    try {
      const { filePath, lineNumber, oldText, newText } = request
      
      // Resolve the file path relative to the project root
      const absolutePath = path.resolve(process.cwd(), filePath)
      
      console.log(`Updating file: ${absolutePath}`)
      console.log(`Line ${lineNumber}: "${oldText}" -> "${newText}"`)
      
      // Read the file
      const fileContent = await fs.readFile(absolutePath, 'utf-8')
      const lines = fileContent.split('\n')
      
      // Check if the line number is valid
      if (lineNumber < 1 || lineNumber > lines.length) {
        return {
          success: false,
          message: `Invalid line number: ${lineNumber}. File has ${lines.length} lines.`
        }
      }
      
      // Get the line to update (0-indexed)
      const lineIndex = lineNumber - 1
      const currentLine = lines[lineIndex]
      
      // Check if the current line contains the expected text
      if (!currentLine.includes(oldText)) {
        console.log(`Warning: Line ${lineNumber} does not contain expected text "${oldText}"`)
        console.log(`Current line content: "${currentLine}"`)
        
        // Try to find the text in nearby lines
        const searchRange = 5 // Look 5 lines before and after
        const startLine = Math.max(0, lineIndex - searchRange)
        const endLine = Math.min(lines.length, lineIndex + searchRange + 1)
        
        for (let i = startLine; i < endLine; i++) {
          if (lines[i].includes(oldText)) {
            console.log(`Found text at line ${i + 1}, updating that line instead`)
            lines[i] = lines[i].replace(oldText, newText)
            
            // Write the updated content back to the file
            await fs.writeFile(absolutePath, lines.join('\n'), 'utf-8')
            
            return {
              success: true,
              message: `Updated line ${i + 1} in ${filePath}`
            }
          }
        }
        
        return {
          success: false,
          message: `Could not find text "${oldText}" in file ${filePath} around line ${lineNumber}`
        }
      }
      
      // Update the line
      lines[lineIndex] = currentLine.replace(oldText, newText)
      
      // Write the updated content back to the file
      await fs.writeFile(absolutePath, lines.join('\n'), 'utf-8')
      
      return {
        success: true,
        message: `Successfully updated line ${lineNumber} in ${filePath}`
      }
      
    } catch (error) {
      console.error('Error updating file:', error)
      return {
        success: false,
        message: `Failed to update file: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
  
  /**
   * Updates multiple lines in a file
   */
  static async updateMultipleLines(requests: FileUpdateRequest[]): Promise<{ success: boolean; message: string; results: Array<{ success: boolean; message: string }> }> {
    const results = []
    
    for (const request of requests) {
      const result = await this.updateLine(request)
      results.push(result)
      
      if (!result.success) {
        return {
          success: false,
          message: `Failed to update file: ${result.message}`,
          results
        }
      }
    }
    
    return {
      success: true,
      message: `Successfully updated ${requests.length} lines`,
      results
    }
  }
  
  /**
   * Gets the current content of a specific line
   */
  static async getLineContent(filePath: string, lineNumber: number): Promise<{ success: boolean; content?: string; message: string }> {
    try {
      const absolutePath = path.resolve(process.cwd(), filePath)
      const fileContent = await fs.readFile(absolutePath, 'utf-8')
      const lines = fileContent.split('\n')
      
      if (lineNumber < 1 || lineNumber > lines.length) {
        return {
          success: false,
          message: `Invalid line number: ${lineNumber}. File has ${lines.length} lines.`
        }
      }
      
      return {
        success: true,
        content: lines[lineNumber - 1],
        message: `Retrieved line ${lineNumber} from ${filePath}`
      }
      
    } catch (error) {
      console.error('Error reading file:', error)
      return {
        success: false,
        message: `Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
} 