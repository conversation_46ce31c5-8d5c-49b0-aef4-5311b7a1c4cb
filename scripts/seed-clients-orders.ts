import { prisma } from '../src/config/prisma'

export async function seedClientsAndOrders() {
  try {
    // Sample client data
    const sampleClients = [
      {
        companyname: 'GreenTech Solutions',
        contactname: '<PERSON>',
        contactemail: '<EMAIL>',
        contactphone: '******-0123',
        companywebsite: 'https://greentech.com',
        address: '123 Tech Street',
        city: 'San Francisco',
        state: 'CA',
        country: 'USA',
        zipcode: '94105',
        notes: 'Sustainable technology company focused on green solutions',
      },
      {
        companyname: 'MedTech Innovations',
        contactname: 'Dr. <PERSON>',
        contactemail: '<EMAIL>',
        contactphone: '******-0124',
        companywebsite: 'https://medtech.com',
        address: '456 Medical Ave',
        city: 'Boston',
        state: 'MA',
        country: 'USA',
        zipcode: '02101',
        notes: 'Healthcare technology and medical device company',
      },
      {
        companyname: 'FinanceFlow Corp',
        contactname: '<PERSON>',
        contactemail: '<EMAIL>',
        contactphone: '******-0125',
        companywebsite: 'https://financeflow.com',
        address: '789 Finance Blvd',
        city: 'New York',
        state: 'NY',
        country: 'USA',
        zipcode: '10001',
        notes: 'Financial services and investment management',
      },
      {
        companyname: 'EduTech Academy',
        contactname: 'Lisa Wang',
        contactemail: '<EMAIL>',
        contactphone: '******-0126',
        companywebsite: 'https://edutech.com',
        address: '321 Learning Lane',
        city: 'Austin',
        state: 'TX',
        country: 'USA',
        zipcode: '73301',
        notes: 'Online education and e-learning platform',
      },
      {
        companyname: 'LogiFlow Solutions',
        contactname: 'David Kim',
        contactemail: '<EMAIL>',
        contactphone: '******-0127',
        companywebsite: 'https://logiflow.com',
        address: '654 Supply Chain St',
        city: 'Chicago',
        state: 'IL',
        country: 'USA',
        zipcode: '60601',
        notes: 'Supply chain and logistics management solutions',
      },
    ]

    // Create clients
    const createdClients: any[] = []
    for (const clientData of sampleClients) {
      const existingClient = await prisma.clients.findFirst({
        where: { contactemail: clientData.contactemail },
      })

      if (!existingClient) {
        const client = await prisma.clients.create({
          data: clientData,
        })
        createdClients.push(client)
        console.log(`Created client: ${clientData.companyname}`)
      } else {
        createdClients.push(existingClient)
        console.log(`Client already exists: ${clientData.companyname}`)
      }
    }

    // Sample order data
    const sampleOrders = [
      {
        clientid: createdClients[0].id,
        ordertitle: 'E-commerce Platform Development',
        orderdesc: 'E-commerce platform development project',
        status: 'CONFIRMED',
        ordertotalamount: 45000,
        orderdate: new Date('2024-01-15'),
      },
      {
        clientid: createdClients[1].id,
        ordertitle: 'Healthcare Mobile Application',
        orderdesc: 'Healthcare mobile application development',
        status: 'CONFIRMED',
        ordertotalamount: 65000,
        orderdate: new Date('2024-02-01'),
      },
      {
        clientid: createdClients[2].id,
        ordertitle: 'Financial Analytics Dashboard',
        orderdesc: 'Financial analytics dashboard',
        status: 'CONFIRMED',
        ordertotalamount: 85000,
        orderdate: new Date('2024-01-10'),
      },
      {
        clientid: createdClients[3].id,
        ordertitle: 'Online Learning Platform',
        orderdesc: 'Online learning platform development',
        status: 'PENDING',
        ordertotalamount: 55000,
        orderdate: new Date('2024-03-01'),
      },
      {
        clientid: createdClients[4].id,
        ordertitle: 'Logistics Management System',
        orderdesc: 'Logistics management system',
        status: 'CONFIRMED',
        ordertotalamount: 70000,
        orderdate: new Date('2024-02-15'),
      },
    ]

    // Create orders
    for (const orderData of sampleOrders) {
      const existingOrder = await prisma.orders.findFirst({
        where: { 
          clientid: orderData.clientid,
          ordertitle: orderData.ordertitle 
        },
      })

      if (!existingOrder) {
        await prisma.orders.create({
          data: orderData,
        })
        console.log(`Created order: ${orderData.ordertitle}`)
      } else {
        console.log(`Order already exists: ${orderData.ordertitle}`)
      }
    }

    console.log('Clients and orders seeding completed!')
  } catch (error) {
    console.error('Error seeding clients and orders:', error)
  }
}

// Run this function to seed clients and orders
if (require.main === module) {
  seedClientsAndOrders()
    .then(() => {
      console.log('Seeding finished')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}
