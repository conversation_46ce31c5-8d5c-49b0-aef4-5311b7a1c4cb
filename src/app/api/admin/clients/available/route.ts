import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, requireAdmin } from '@/services/api/api-utils'

// GET /api/admin/clients/available - Get clients available for linking (not already linked to users)
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const url = new URL(request.url)
  const excludeUserId = url.searchParams.get('excludeUserId') // For edit mode

  // Get all active clients (user wants to see all clients with scrollbar)
  const where: any = {
    isactive: true
  }

  const clients = await prisma.clients.findMany({
    where,
    select: {
      id: true,
      companyname: true,
      contactname: true,
      contactemail: true
    },
    orderBy: {
      companyname: 'asc'
    }
  })

  // Convert BigInt to number for JSON serialization
  const serializedClients = clients.map(client => ({
    value: Number(client.id),
    label: `${client.companyname} (${client.contactname})`,
    email: client.contactemail
  }))

  return Response.json({
    success: true,
    data: serializedClients
  })
})
