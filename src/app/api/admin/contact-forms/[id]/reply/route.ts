import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { transformFromDbFields } from '@/lib/utils/data-transform'
import { z } from 'zod'
import { readFile } from 'fs/promises'
import path from 'path'

interface RouteParams {
  params: { id: string }
}

// Attachment schema
const attachmentSchema = z.object({
  id: z.string(),
  filename: z.string(),
  size: z.number(),
  mimeType: z.string(),
  url: z.string(),
  path: z.string(),
})

// Reply schema
const replySchema = z.object({
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  markAsResolved: z.boolean().default(false),
  attachments: z.array(attachmentSchema).default([]),
})

// POST /api/admin/contact-forms/[id]/reply - Reply to a contact form
export const POST = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const resolvedParams = await params // Await params for Next.js 15
  const id = parseInt(resolvedParams.id)
  if (isNaN(id)) {
    throw new ApiError('Invalid contact form ID', 400)
  }

  // Validate the reply data
  const validate = validateRequest(replySchema)
  const data = await validate(request)

  // Get the contact form
  const contactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(id) }
  })

  if (!contactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  // Send reply email
  try {
    const { sendEmail, EmailAttachment } = await import('@/lib/email')

    const replyHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Reply to Your Contact Form Submission</h2>
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Original Subject:</strong> ${contactForm.subject}</p>
          <p><strong>Your Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin: 10px 0;">
            ${contactForm.message}
          </div>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #ddd;">
          <p><strong>Our Reply:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin: 10px 0;">
            ${data.message}
          </div>
          ${data.attachments.length > 0 ? `
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #ddd;">
          <p><strong>Attachments:</strong></p>
          <ul style="margin: 10px 0; padding-left: 20px;">
            ${data.attachments.map(att => `<li>${att.filename}</li>`).join('')}
          </ul>
          ` : ''}
        </div>
        <p style="color: #666; font-size: 14px;">
          Thank you for contacting us. If you have any further questions, please don't hesitate to reach out.
        </p>
        <p style="color: #666; font-size: 14px;">
          Best regards,<br>
          The Technoloway Team
        </p>
      </div>
    `

    // Prepare email attachments
    const emailAttachments: EmailAttachment[] = []

    if (data.attachments.length > 0) {
      for (const attachment of data.attachments) {
        try {
          // Read file from disk
          const filePath = path.resolve(attachment.path)
          const fileContent = await readFile(filePath)

          emailAttachments.push({
            filename: attachment.filename,
            content: fileContent,
            contentType: attachment.mimeType
          })
        } catch (fileError) {
          console.warn(`Failed to read attachment ${attachment.filename}:`, fileError)
          // Continue with other attachments
        }
      }
    }

    const emailSent = await sendEmail(
      contactForm.email,
      data.subject,
      replyHtml,
      undefined, // from (use default)
      emailAttachments
    )

    if (!emailSent) {
      throw new ApiError('Failed to send reply email', 500)
    }

    // Update contact form status
    const updateData: any = {
      isread: true,
      readat: new Date(),
    }

    if (data.markAsResolved) {
      updateData.status = 'Resolved'
    }

    const updatedContactForm = await prisma.contactforms.update({
      where: { id: BigInt(id) },
      data: updateData
    })

    return successResponse({
      contactForm: transformFromDbFields.contactForm(updatedContactForm),
      emailSent: true
    }, 'Reply sent successfully')

  } catch (emailError) {
    console.error('Failed to send reply email:', emailError)
    throw new ApiError('Failed to send reply email', 500)
  }
})
