import { prisma } from '@/config/prisma'
import { schemas } from '@/lib/utils/validations'
import {
    buildSearchQuery,
    buildSortQuery,
    getPaginationParams,
    getQueryParams,
    paginatedResponse,
    requireAdmin,
    successResponse,
    validateRequest,
    with<PERSON>rror<PERSON>and<PERSON>
} from '@/services/api/api-utils'
import { NextRequest } from 'next/server'

// GET /api/admin/job-applications - Get all job applications with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { searchParams } = new URL(request.url)
  const jobListingId = searchParams.get('jobListingId')
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['applicantName', 'applicantEmail', 'applicantPhone'])

  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  // Add jobListingId filter if provided
  if (jobListingId) {
    searchQuery.joblistingid = BigInt(jobListingId)
  }

  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get job applications with pagination
  const [jobApplications, total] = await Promise.all([
    prisma.jobapplications.findMany({
      where: searchQuery,
      include: {
        joblistings: {
          select: {
            id: true,
            title: true,
            location: true,
            employmenttype: true,
            isactive: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.jobapplications.count({ where: searchQuery })
  ])

  return paginatedResponse(jobApplications, page, limit, total)
})

// POST /api/admin/job-applications - Create a new job application (usually from public form)
export const POST = withErrorHandler(async (request: NextRequest) => {
  const validate = validateRequest(schemas.jobApplication.create)
  const data = await validate(request)

  // Check if job listing exists and is active
  const jobListing = await prisma.joblistings.findUnique({
    where: { id: BigInt(data.jobListingId) },
  })

  if (!jobListing) {
    throw new Error('Job listing not found')
  }

  if (!jobListing.isactive) {
    throw new Error('Job listing is no longer active')
  }

  const jobApplication = await prisma.jobapplications.create({
    data: {
      applicantname: data.applicantName,
      applicantemail: data.applicantEmail,
      applicantphone: data.applicantPhone,
      resumeurl: data.resumeUrl,
      coverletter: data.coverLetter,
      joblistingid: BigInt(data.jobListingId),
      status: data.status || 'PENDING',
      notes: data.notes,
    },
    include: {
      joblistings: {
        select: {
          id: true,
          title: true,
          location: true,
          employmenttype: true
        }
      }
    }
  })

  return successResponse(jobApplication, 'Job application submitted successfully', 201)
})
