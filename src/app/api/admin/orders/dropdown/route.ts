import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  withE<PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/orders/dropdown - Get orders for dropdown selection
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const orders = await prisma.orderss.findMany({
    select: {
      id: true,
      ordernumber: true,
      status: true,
      totalamount: true,
      client: {
        select: {
          companyname: true,
        },
      },
    },
    orderBy: {
      createdat: 'desc',
    },
  })

  // Format for dropdown
  const formattedOrders = orders.map(order => ({
    value: order.id,
    label: `${order.orderNumber} - ${order.client.companyname} (${order.status})`,
    ordernumber: order.orderNumber,
    clientname: order.client.companyname,
    status: order.status,
    totalamount: order.totalamount,
  }))

  return successResponse(formattedOrders)
})
