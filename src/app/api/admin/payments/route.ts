import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  ApiError,
  requireAdmin,
  getQueryParams,
  getPaginationParams
} from '@/services/api/api-utils'

// GET /api/admin/payments - Get all payments with pagination and filtering
export const GET = withError<PERSON>andler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, filter, sort } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}

  // Search functionality
  if (search) {
    where.OR = [
      { paymentmethod: { contains: search, mode: 'insensitive' } },
      { status: { contains: search, mode: 'insensitive' } },
      { notes: { contains: search, mode: 'insensitive' } },
      { reference: { contains: search, mode: 'insensitive' } },
      { transactionid: { contains: search, mode: 'insensitive' } },
      {
        invoices: {
          OR: [
            { description: { contains: search, mode: 'insensitive' } },
            { clients: { companyname: { contains: search, mode: 'insensitive' } } },
            { projects: { name: { contains: search, mode: 'insensitive' } } }
          ]
        }
      }
    ]
  }

  // Filter functionality
  if (filter) {
    try {
      const filters = JSON.parse(filter)
      
      if (filters.status) {
        where.status = filters.status
      }
      
      if (filters.paymentMethod) {
        where.paymentmethod = filters.paymentMethod
      }
      
      if (filters.dateFrom || filters.dateTo) {
        where.paymentdate = {}
        if (filters.dateFrom) {
          where.paymentdate.gte = new Date(filters.dateFrom)
        }
        if (filters.dateTo) {
          where.paymentdate.lte = new Date(filters.dateTo)
        }
      }

      if (filters.amountMin || filters.amountMax) {
        where.amount = {}
        if (filters.amountMin) {
          where.amount.gte = parseFloat(filters.amountMin)
        }
        if (filters.amountMax) {
          where.amount.lte = parseFloat(filters.amountMax)
        }
      }

      if (filters.clientId) {
        where.invoices = {
          clientid: parseInt(filters.clientId)
        }
      }

      if (filters.projectId) {
        where.invoices = {
          projectid: parseInt(filters.projectId)
        }
      }
    } catch (error) {
      console.error('Error parsing filter:', error)
    }
  }

  // Build orderBy clause
  let orderBy: any = { paymentdate: 'desc' } // Default sort
  
  if (sort) {
    const [field, direction] = sort.split(':')
    const validFields = ['amount', 'paymentdate', 'paymentmethod', 'status', 'createdat']
    const validDirections = ['asc', 'desc']
    
    if (validFields.includes(field) && validDirections.includes(direction)) {
      orderBy = { [field]: direction }
    }
  }

  // Get payments with related data
  const [payments, total] = await Promise.all([
    prisma.payments.findMany({
      where,
      include: {
        invoices: {
          select: {
            id: true,
            totalamount: true,
            status: true,
            duedate: true,
            description: true,
            projects: {
              select: {
                id: true,
                name: true
              }
            },
            clients: {
              select: {
                id: true,
                companyname: true
              }
            }
          }
        }
      },
      orderBy,
      skip,
      take
    }),
    prisma.payments.count({ where })
  ])

  // Transform the response
  const transformedPayments = payments.map(payment => ({
    id: Number(payment.id),
    amount: Number(payment.amount),
    paymentDate: payment.paymentdate.toISOString(),
    paymentMethod: payment.paymentmethod,
    status: payment.status,
    notes: payment.notes,
    reference: payment.reference,
    transactionId: payment.transactionid,
    processingFee: payment.processingfee ? Number(payment.processingfee) : null,
    invoiceId: Number(payment.invoiceid),
    createdAt: payment.createdat.toISOString(),
    updatedAt: payment.updatedat?.toISOString(),
    currency: payment.currency,
    promoCode: payment.promocode,
    discount: payment.discount ? Number(payment.discount) : 0,
    emailReceipt: payment.emailreceipt,
    receiptEmail: payment.receiptemail,
    termsAccepted: payment.termsaccepted,
    paymentDetails: payment.paymentdetails,
    stripePaymentIntentId: payment.stripepaymentintentid,
    stripeClientSecret: payment.stripeclientsecret,
    invoice: payment.invoices ? {
      id: Number(payment.invoices.id),
      totalAmount: Number(payment.invoices.totalamount),
      status: payment.invoices.status,
      dueDate: payment.invoices.duedate.toISOString(),
      description: payment.invoices.description,
      project: payment.invoices.projects ? {
        id: Number(payment.invoices.projects.id),
        name: payment.invoices.projects.name
      } : null,
      client: payment.invoices.clients ? {
        id: Number(payment.invoices.clients.id),
        companyname: payment.invoices.clients.companyname
      } : null
    } : null
  }))

  return paginatedResponse(transformedPayments, page, take, total)
})

// POST /api/admin/payments - Create a new payment
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()
  const {
    invoiceId,
    amount,
    paymentMethod,
    paymentDate,
    notes,
    status,
    reference,
    transactionId,
    processingFee,
    currency,
    promoCode,
    discount,
    emailReceipt,
    receiptEmail,
    termsAccepted,
    paymentDetails,
    stripePaymentIntentId,
    stripeClientSecret
  } = body

  // Validate required fields
  if (!invoiceId || !amount || amount <= 0 || !paymentMethod || !paymentDate) {
    throw new ApiError('Invoice ID, valid amount, payment method, and payment date are required', 400)
  }

  // Verify invoice exists
  const invoice = await prisma.invoices.findUnique({
    where: { id: parseInt(invoiceId) },
    select: {
      id: true,
      totalamount: true,
      status: true
    }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  // Check if payment amount exceeds remaining balance
  const existingPayments = await prisma.payments.findMany({
    where: { invoiceid: parseInt(invoiceId) },
    select: { amount: true }
  })

  const totalPaid = existingPayments.reduce((sum, payment) => sum + Number(payment.amount), 0)
  const remainingBalance = Number(invoice.totalamount) - totalPaid

  if (amount > remainingBalance) {
    throw new ApiError(`Payment amount ($${amount}) exceeds remaining balance ($${remainingBalance.toFixed(2)})`, 400)
  }

  // Create the payment
  const payment = await prisma.payments.create({
    data: {
      invoiceid: parseInt(invoiceId),
      amount: amount,
      paymentdate: new Date(paymentDate),
      paymentmethod: paymentMethod,
      status: status || 'completed',
      notes: notes || null,
      reference: reference || null,
      transactionid: transactionId || null,
      processingfee: processingFee || null,
      currency: currency || 'USD',
      promocode: promoCode || null,
      discount: discount || 0,
      emailreceipt: emailReceipt || false,
      receiptemail: receiptEmail || null,
      termsaccepted: termsAccepted !== undefined ? termsAccepted : true,
      paymentdetails: paymentDetails || null,
      stripepaymentintentid: stripePaymentIntentId || null,
      stripeclientsecret: stripeClientSecret || null
    },
    include: {
      invoices: {
        select: {
          id: true,
          totalamount: true,
          status: true,
          duedate: true,
          description: true,
          projects: {
            select: {
              id: true,
              name: true
            }
          },
          clients: {
            select: {
              id: true,
              companyname: true
            }
          }
        }
      }
    }
  })

  // Update invoice status if fully paid
  const newTotalPaid = totalPaid + amount
  if (newTotalPaid >= Number(invoice.totalamount)) {
    await prisma.invoices.update({
      where: { id: parseInt(invoiceId) },
      data: {
        status: 'paid',
        paidat: new Date()
      }
    })
  } else if (invoice.status === 'draft') {
    // Update to pending if it was draft and now has partial payment
    await prisma.invoices.update({
      where: { id: parseInt(invoiceId) },
      data: { status: 'partial' }
    })
  }

  // Transform the response
  const transformedPayment = {
    id: Number(payment.id),
    amount: Number(payment.amount),
    paymentDate: payment.paymentdate.toISOString(),
    paymentMethod: payment.paymentmethod,
    status: payment.status,
    notes: payment.notes,
    reference: payment.reference,
    transactionId: payment.transactionid,
    processingFee: payment.processingfee ? Number(payment.processingfee) : null,
    invoiceId: Number(payment.invoiceid),
    createdAt: payment.createdat.toISOString(),
    updatedAt: payment.updatedat?.toISOString(),
    currency: payment.currency,
    promoCode: payment.promocode,
    discount: payment.discount ? Number(payment.discount) : 0,
    emailReceipt: payment.emailreceipt,
    receiptEmail: payment.receiptemail,
    termsAccepted: payment.termsaccepted,
    paymentDetails: payment.paymentdetails,
    stripePaymentIntentId: payment.stripepaymentintentid,
    stripeClientSecret: payment.stripeclientsecret,
    invoice: payment.invoices ? {
      id: Number(payment.invoices.id),
      totalAmount: Number(payment.invoices.totalamount),
      status: payment.invoices.status,
      dueDate: payment.invoices.duedate.toISOString(),
      description: payment.invoices.description,
      project: payment.invoices.projects ? {
        id: Number(payment.invoices.projects.id),
        name: payment.invoices.projects.name
      } : null,
      client: payment.invoices.clients ? {
        id: Number(payment.invoices.clients.id),
        companyname: payment.invoices.clients.companyname
      } : null
    } : null
  }

  return successResponse(transformedPayment, 'Payment created successfully')
})
