import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse, 
  validateRequest,
  requireAdmin,
  ApiError
} from '@/services/api/api-utils'
import { updateServiceOptionFeatureSchema } from '@/lib/utils/validations'

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

// GET /api/admin/service-option-features/[id] - Get a specific service option feature
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  const feature = await prisma.serviceoptionfeatures.findUnique({
    where: { id: BigInt(id) },
    include: {
      serviceoptions: {
        select: {
          id: true,
          optname: true,
          services: {
            select: {
              id: true,
              name: true,
              categories: {
                select: {
                  id: true,
                  categname: true,
                },
              },
            },
          },
        },
      },
      _count: {
        select: {
          orderdetails: true,
        },
      },
    },
  })

  if (!feature) {
    throw new ApiError('Service option feature not found', 404)
  }

  // Transform the response data
  const transformedFeature = {
    id: String(feature.id),
    optionId: String(feature.optid),
    name: feature.featname,
    description: feature.featdesc,
    cost: feature.featcost,
    discountRate: feature.featdiscountrate,
    totalDiscount: feature.feattotaldiscount,
    isIncluded: feature.isincluded,
    createdAt: feature.createdat,
    updatedAt: feature.updatedat,
    option: feature.serviceoptions ? {
      id: String(feature.serviceoptions.id),
      name: feature.serviceoptions.optname,
      service: feature.serviceoptions.services ? {
        id: String(feature.serviceoptions.services.id),
        name: feature.serviceoptions.services.name,
        category: feature.serviceoptions.services.categories ? {
          id: String(feature.serviceoptions.services.categories.id),
          name: feature.serviceoptions.services.categories.categname,
        } : null,
      } : null,
    } : null,
    _count: {
      orderDetails: feature._count.orderdetails,
    },
  }

  return successResponse(transformedFeature)
})

// PUT /api/admin/service-option-features/[id] - Update a specific service option feature
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  const validate = validateRequest(updateServiceOptionFeatureSchema)
  const data = await validate(request)

  // Check if feature exists
  const existingFeature = await prisma.serviceoptionfeatures.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingFeature) {
    throw new ApiError('Service option feature not found', 404)
  }

  // If optionId is being changed, check if the new option exists
  if (data.optionId && data.optionId !== String(existingFeature.optid)) {
    const serviceOption = await prisma.serviceoptions.findUnique({
      where: { id: Number(data.optionId) },
    })

    if (!serviceOption) {
      throw new ApiError('Service option not found', 404)
    }
  }

  // Prepare update data
  const updateData: any = {
    updatedat: new Date(),
  }

  if (data.optionId) updateData.optid = Number(data.optionId)
  if (data.name) updateData.featname = data.name
  if (data.description !== undefined) updateData.featdesc = data.description
  if (data.cost !== undefined) updateData.featcost = data.cost
  if (data.discountRate !== undefined) updateData.featdiscountrate = data.discountRate
  if (data.totalDiscount !== undefined) updateData.feattotaldiscount = data.totalDiscount
  if (data.isIncluded !== undefined) updateData.isincluded = data.isIncluded

  const updatedFeature = await prisma.serviceoptionfeatures.update({
    where: { id: BigInt(id) },
    data: updateData,
    include: {
      serviceoptions: {
        select: {
          id: true,
          optname: true,
          services: {
            select: {
              id: true,
              name: true,
              categories: {
                select: {
                  id: true,
                  categname: true,
                },
              },
            },
          },
        },
      },
      _count: {
        select: {
          orderdetails: true,
        },
      },
    },
  })

  // Transform the response data
  const transformedFeature = {
    id: String(updatedFeature.id),
    optionId: String(updatedFeature.optid),
    name: updatedFeature.featname,
    description: updatedFeature.featdesc,
    cost: updatedFeature.featcost,
    discountRate: updatedFeature.featdiscountrate,
    totalDiscount: updatedFeature.feattotaldiscount,
    isIncluded: updatedFeature.isincluded,
    createdAt: updatedFeature.createdat,
    updatedAt: updatedFeature.updatedat,
    option: updatedFeature.serviceoptions ? {
      id: String(updatedFeature.serviceoptions.id),
      name: updatedFeature.serviceoptions.optname,
      service: updatedFeature.serviceoptions.services ? {
        id: String(updatedFeature.serviceoptions.services.id),
        name: updatedFeature.serviceoptions.services.name,
        category: updatedFeature.serviceoptions.services.categories ? {
          id: String(updatedFeature.serviceoptions.services.categories.id),
          name: updatedFeature.serviceoptions.services.categories.categname,
        } : null,
      } : null,
    } : null,
    _count: {
      orderDetails: updatedFeature._count.orderdetails,
    },
  }

  return successResponse(transformedFeature, 'Service option feature updated successfully')
})

// DELETE /api/admin/service-option-features/[id] - Delete a specific service option feature
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if feature exists
  const existingFeature = await prisma.serviceoptionfeatures.findUnique({
    where: { id: BigInt(id) },
    include: {
      orderdetails: { select: { id: true } },
    },
  })

  if (!existingFeature) {
    throw new ApiError('Service option feature not found', 404)
  }

  // Check if feature is being used in orders
  if (existingFeature.orderdetails.length > 0) {
    throw new ApiError(
      'Cannot delete service option feature that is used in orders. Please remove it from orders first.',
      400
    )
  }

  // Delete the feature
  await prisma.serviceoptionfeatures.delete({
    where: { id: BigInt(id) },
  })

  return successResponse(null, 'Service option feature deleted successfully')
})
