import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/config/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'

interface RouteParams {
  params: Promise<{ id: string }>
}

// PUT /api/admin/settings/[id] - Update a site setting
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }
    const { id } = await params
    const body = await request.json()

    // Allow empty values for some field types
    const value = body.value || ''

    // Check if site setting exists
    const existingSetting = await prisma.sitesettings.findUnique({
      where: { id: BigInt(id) },
    })

    if (!existingSetting) {
      return NextResponse.json(
        { success: false, error: 'Site setting not found' },
        { status: 404 }
      )
    }

    // Update the setting
    const siteSetting = await prisma.sitesettings.update({
      where: { id: BigInt(id) },
      data: {
        key: body.key || existingSetting.key,
        value: value,
        description: body.description || existingSetting.description,
        category: body.category || existingSetting.category,
        fieldtype: body.fieldType || existingSetting.fieldtype,
        options: body.options || existingSetting.options,
        ispublic: body.ispublic !== undefined ? body.ispublic : existingSetting.ispublic,
        isactive: body.isactive !== undefined ? body.isactive : existingSetting.isactive,
        updatedat: new Date(),
      }
    })

    // Convert BigInt to string for JSON serialization
    const serializedSetting = {
      ...siteSetting,
      id: siteSetting.id.toString(),
      createdat: siteSetting.createdat?.toISOString(),
      updatedat: siteSetting.updatedat?.toISOString(),
    }

    return NextResponse.json({
      success: true,
      data: serializedSetting,
      message: 'Site setting updated successfully'
    })

  } catch (error) {
    console.error('Error updating site setting:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/settings/[id] - Delete a site setting
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }
    const { id } = await params

    console.log('Attempting to delete setting with ID:', id)

    // Check if site setting exists
    const existingSetting = await prisma.sitesettings.findUnique({
      where: { id: BigInt(id) },
    })

    if (!existingSetting) {
      console.log('Setting not found:', id)
      return NextResponse.json(
        { success: false, error: 'Site setting not found' },
        { status: 404 }
      )
    }

    console.log('Found setting, deleting:', existingSetting.key)

    // Delete the setting
    await prisma.sitesettings.delete({
      where: { id: BigInt(id) },
    })

    console.log('Setting deleted successfully')

    return NextResponse.json({
      success: true,
      message: 'Site setting deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting site setting:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
