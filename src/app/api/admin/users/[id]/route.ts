import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import bcrypt from 'bcryptjs'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/users/[id] - Get a specific user
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const user = await prisma.users.findUnique({
    where: { id: parseInt(id) },
    select: {
      id: true,
      email: true,
      emailverified: true,
      firstname: true,
      lastname: true,
      imageurl: true,
      role: true,
      isactive: true,
      createdat: true,
      updatedat: true,
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true
        },
        take: 1
      },
      _count: {
        select: {
          clients: true,
          auditlogs: true
        }
      }
    }
  })

  if (!user) {
    throw new ApiError('User not found', 404)
  }

  // Convert BigInt to number for JSON serialization
  const serializedUser = {
    ...user,
    id: Number(user.id),
    linkedclient: user.clients[0] ? {
      id: Number(user.clients[0].id),
      companyname: user.clients[0].companyname,
      contactname: user.clients[0].contactname
    } : null,
    linkedclientid: user.clients[0] ? Number(user.clients[0].id) : null,
    clients: undefined // Remove the clients array from response
  }

  return Response.json({
    success: true,
    data: serializedUser
  })
})

// PUT /api/admin/users/[id] - Update a user
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Validate the data
  const data = schemas.user.update.parse(body)

  // Check if user exists
  const existingUser = await prisma.users.findUnique({
    where: { id: parseInt(id) },
  })

  if (!existingUser) {
    throw new ApiError('User not found', 404)
  }

  // Check if email is being changed and if it conflicts with another user
  if (data.email && data.email !== existingUser.email) {
    const emailConflict = await prisma.users.findFirst({
      where: {
        email: data.email,
        id: { not: parseInt(id) }
      },
    })

    if (emailConflict) {
      throw new ApiError('A user with this email already exists', 400)
    }
  }

  // Prepare update data
  const updateData: any = { updatedat: new Date() }
  if (data.email !== undefined) updateData.email = data.email
  if (data.firstname !== undefined) updateData.firstname = data.firstname
  if (data.lastname !== undefined) updateData.lastname = data.lastname
  if (data.imageurl !== undefined) updateData.imageurl = data.imageurl
  if (data.role !== undefined) updateData.role = data.role
  if (data.isactive !== undefined) updateData.isactive = data.isactive

  // Hash password if provided
  if (data.password) {
    updateData.password = await bcrypt.hash(data.password, 12)
  }

  // Handle client linking
  if (data.linkedclientid !== undefined) {
    const newClientId = data.linkedclientid ? Number(data.linkedclientid) : null

    // Get current client linked to this user
    const currentClient = await prisma.clients.findFirst({
      where: { userid: BigInt(id) }
    })

    // If linking to a new client, validate it exists and is not already linked
    if (newClientId) {
      const targetClient = await prisma.clients.findUnique({
        where: { id: BigInt(newClientId) }
      })

      if (!targetClient) {
        throw new ApiError('Selected client does not exist', 400)
      }

      if (targetClient.userid && Number(targetClient.userid) !== parseInt(id)) {
        throw new ApiError('Selected client is already linked to another user', 400)
      }
    }

    // Update client linking in a transaction
    await prisma.$transaction(async (tx) => {
      // Unlink current client if exists
      if (currentClient) {
        await tx.clients.update({
          where: { id: currentClient.id },
          data: { userid: null }
        })
      }

      // Link new client if provided
      if (newClientId) {
        await tx.clients.update({
          where: { id: BigInt(newClientId) },
          data: { userid: BigInt(id) }
        })
      }
    })
  }

  const user = await prisma.users.update({
    where: { id: parseInt(id) },
    data: updateData,
    select: {
      id: true,
      email: true,
      emailverified: true,
      firstname: true,
      lastname: true,
      imageurl: true,
      role: true,
      isactive: true,
      createdat: true,
      updatedat: true,
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true
        },
        take: 1
      }
    }
  })

  // Convert BigInt to number for JSON serialization
  const serializedUser = {
    ...user,
    id: Number(user.id),
    linkedclient: user.clients[0] ? {
      id: Number(user.clients[0].id),
      companyname: user.clients[0].companyname,
      contactname: user.clients[0].contactname
    } : null,
    linkedclientid: user.clients[0] ? Number(user.clients[0].id) : null,
    clients: undefined // Remove the clients array from response
  }

  return Response.json({
    success: true,
    data: serializedUser,
    message: 'User updated successfully'
  })
})

// PATCH /api/admin/users/[id] - Partial update (for status changes)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Prepare update data
  const updateData: any = { updatedat: new Date() }

  if (body.isactive !== undefined) updateData.isactive = body.isactive

  const user = await prisma.users.update({
    where: { id: parseInt(id) },
    data: updateData,
    select: {
      id: true,
      email: true,
      emailverified: true,
      firstname: true,
      lastname: true,
      imageurl: true,
      role: true,
      isactive: true,
      createdat: true,
      updatedat: true
    }
  })

  // Convert BigInt to number for JSON serialization
  const serializedUser = {
    ...user,
    id: Number(user.id)
  }

  return Response.json({
    success: true,
    data: serializedUser,
    message: 'User updated successfully'
  })
})

// DELETE /api/admin/users/[id] - Delete a user
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if user has associated data that should be preserved
  const userWithData = await prisma.users.findUnique({
    where: { id: parseInt(id) },
    include: {
      _count: {
        select: {
          clients: true,
          auditlogs: true
        }
      }
    }
  })

  if (!userWithData) {
    throw new ApiError('User not found', 404)
  }

  // If user has important data, prevent deletion to maintain data integrity
  if (userWithData._count.clients > 0) {
    throw new ApiError(
      `Cannot delete user with associated clients. This user has ${userWithData._count.clients} client(s) associated with them. Please reassign or remove the clients first, or deactivate the user instead.`,
      400
    )
  }

  await prisma.users.delete({
    where: { id: parseInt(id) }
  })

  return Response.json({
    success: true,
    message: 'User deleted successfully'
  })
})
