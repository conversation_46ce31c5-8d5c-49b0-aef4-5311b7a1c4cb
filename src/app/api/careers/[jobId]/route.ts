import { prisma } from '@/config/prisma';
import { NextResponse } from 'next/server';

export async function GET(
  req: Request,
  { params }: { params: { jobId: string } }
) {
  try {
    const job = await prisma.joblistings.findUnique({
      where: { id: BigInt(params.jobId), isactive: true },
      select: {
        id: true,
        title: true,
        location: true,
        employmenttype: true,
        description: true,
        requirements: true,
        salarymin: true,
        salarymax: true,
        salarycurrency: true,
        isremote: true,
        isactive: true,
        createdat: true,
        updatedat: true,
      },
    });
    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    // Convert BigInt to string for JSON serialization
    const serializedJob = {
      ...job,
      id: job.id.toString(),
    };

    return NextResponse.json(serializedJob);
  } catch (error) {
    console.error('Error fetching job details:', error);
    return NextResponse.json({ error: 'Failed to fetch job details.' }, { status: 500 });
  }
}
