import { prisma } from '@/config/prisma';
import { writeFile } from 'fs/promises';
import { NextResponse } from 'next/server';
import path from 'path';

export const runtime = 'nodejs';

export async function POST(req: Request) {
  try {
    const formData = await req.formData();

    // Extract all form fields
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const coverletter = formData.get('coverletter') as string;
    const jobId = Number(formData.get('jobId'));
    const resume = formData.get('resume') as File;

    // Additional fields from the enhanced form
    const location = formData.get('location') as string;
    const country = formData.get('country') as string;
    const currentPosition = formData.get('currentPosition') as string;
    const currentCompany = formData.get('currentCompany') as string;
    const yearsOfExperience = formData.get('yearsOfExperience') as string;
    const expectedSalary = formData.get('expectedSalary') as string;
    const availabilityDate = formData.get('availabilityDate') as string;
    const workAuthorization = formData.get('workAuthorization') as string;
    const requiresSponsorship = formData.get('requiresSponsorship') as string;
    const remotePreference = formData.get('remotePreference') as string;
    const portfolioUrl = formData.get('portfolioUrl') as string;
    const linkedinUrl = formData.get('linkedinUrl') as string;
    const githubUrl = formData.get('githubUrl') as string;
    const referralSource = formData.get('referralSource') as string;
    const referralDetails = formData.get('referralDetails') as string;

    // Validate required fields
    if (!name || !email || !phone || !coverletter || !resume || !jobId) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }

    if (!location || !country || !currentPosition || !currentCompany || !yearsOfExperience || !availabilityDate) {
      return NextResponse.json({ error: 'Missing required professional information.' }, { status: 400 });
    }

    if (!workAuthorization || !requiresSponsorship || !remotePreference || !referralSource) {
      return NextResponse.json({ error: 'Missing required work preference information.' }, { status: 400 });
    }

    if (resume.type !== 'application/pdf') {
      return NextResponse.json({ error: 'Resume must be a PDF.' }, { status: 400 });
    }

    if (resume.size > 5 * 1024 * 1024) { // 5MB limit
      return NextResponse.json({ error: 'Resume file size must be less than 5MB.' }, { status: 400 });
    }

    // Save resume file
    const buffer = Buffer.from(await resume.arrayBuffer());
    const fileName = `resume-${Date.now()}-${Math.random().toString(36).substring(7)}.pdf`;
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'resumes');
    const filePath = path.join(uploadDir, fileName);
    await writeFile(filePath, buffer);
    const resumeUrl = `/uploads/resumes/${fileName}`;

    // Create comprehensive application data object
    const applicationData = {
      applicantname: name,
      applicantemail: email,
      applicantphone: phone,
      resumeurl: resumeUrl,
      coverletter,
      joblistingid: BigInt(jobId),
      status: 'PENDING',
      // Store additional information in notes field as JSON for now
      // In a production app, you'd want to add these as separate columns
      notes: JSON.stringify({
        location,
        country,
        currentPosition,
        currentCompany,
        yearsOfExperience,
        expectedSalary,
        availabilityDate,
        workAuthorization,
        requiresSponsorship,
        remotePreference,
        portfolioUrl,
        linkedinUrl,
        githubUrl,
        referralSource,
        referralDetails,
        submittedAt: new Date().toISOString()
      })
    };

    // Store application in DB
    await prisma.jobapplications.create({
      data: applicationData,
    });

    return NextResponse.json({
      success: true,
      message: 'Application submitted successfully! We will review your application and get back to you soon.'
    });
  } catch (error) {
    console.error('Error submitting job application:', error);
    return NextResponse.json({ error: 'Failed to submit application. Please try again.' }, { status: 500 });
  }
}
