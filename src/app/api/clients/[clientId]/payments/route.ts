import { NextRequest } from 'next/server'
import { 
  getClientPayments, 
  getPaymentStatusOptions, 
  getPaymentMethodOptions,
  getClientPaymentStats,
  getRecentClientPayments
} from '@/lib/fetchers/payment'
import { clientExists } from '@/lib/fetchers/client'
import {
  withErrorHandler,
  successResponse,
  paginatedResponse,
  ApiError,
  getQueryParams,
  getPaginationParams
} from '@/services/api/api-utils'

interface RouteParams {
  params: Promise<{ clientId: string }>
}

// GET /api/clients/[clientId]/payments - Get payments for a specific client
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const url = new URL(request.url)
    const { page, limit, search, filter } = getQueryParams(request)
    const { skip, take } = getPaginationParams(page, limit)

    // Check if stats are requested
    const includeStats = url.searchParams.get('stats') === 'true'
    const recentOnly = url.searchParams.get('recent') === 'true'

    // If recent payments are requested
    if (recentOnly) {
      const recentLimit = parseInt(url.searchParams.get('limit') || '5')
      const recentPayments = await getRecentClientPayments(clientId, recentLimit)
      return successResponse({ payments: recentPayments })
    }

    // Extract filters
    let status, paymentMethod, dateFrom, dateTo
    if (filter) {
      const filters = JSON.parse(filter)
      status = filters.status
      paymentMethod = filters.paymentMethod
      dateFrom = filters.dateFrom
      dateTo = filters.dateTo
    }

    const result = await getClientPayments(clientId, {
      page,
      limit: take,
      search,
      status,
      paymentMethod,
      dateFrom,
      dateTo,
    })

    let responseData: any = {
      payments: result.payments,
      pagination: {
        page,
        limit: take,
        total: result.total,
        totalPages: Math.ceil(result.total / take),
      },
    }

    // Include stats if requested
    if (includeStats) {
      const stats = await getClientPaymentStats(clientId)
      responseData.stats = stats
    }

    return successResponse(responseData)
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('Error fetching client payments:', error)
    throw new ApiError('Failed to fetch client payments', 500)
  }
})

// OPTIONS /api/clients/[clientId]/payments - Get available options for payments
export const OPTIONS = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const options = {
      statusOptions: getPaymentStatusOptions(),
      methodOptions: getPaymentMethodOptions(),
      sortOptions: [
        { value: 'amount', label: 'Amount' },
        { value: 'paymentDate', label: 'Payment Date' },
        { value: 'paymentMethod', label: 'Payment Method' },
        { value: 'status', label: 'Status' },
        { value: 'createdAt', label: 'Created Date' },
      ],
    }

    return successResponse(options)
  } catch (error) {
    console.error('Error fetching payment options:', error)
    throw new ApiError('Failed to fetch payment options', 500)
  }
})
