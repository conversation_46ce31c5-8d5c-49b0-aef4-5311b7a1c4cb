import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import fs from 'fs'
import path from 'path'

// GET /api/content/tsx-files - Get list of TSX files
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const projectRoot = process.cwd()
    const srcDir = path.join(projectRoot, 'src')
    
    const tsxFiles = await findTsxFiles(srcDir)
    
    return NextResponse.json({
      success: true,
      files: tsxFiles
    })
  } catch (error) {
    console.error('Error fetching TSX files:', error)
    return NextResponse.json(
      { error: 'Failed to fetch TSX files' },
      { status: 500 }
    )
  }
}

async function findTsxFiles(dir: string, basePath: string = ''): Promise<Array<{
  filePath: string
  lastModified: string
}>> {
  const files: Array<{ filePath: string; lastModified: string }> = []
  
  try {
    const entries = await fs.promises.readdir(dir, { withFileTypes: true })
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name)
      const relativePath = path.join(basePath, entry.name)
      
      if (entry.isDirectory()) {
        // Skip node_modules and .next directories
        if (entry.name === 'node_modules' || entry.name === '.next' || entry.name.startsWith('.')) {
          continue
        }
        
        const subFiles = await findTsxFiles(fullPath, relativePath)
        files.push(...subFiles)
      } else if (entry.isFile() && entry.name.endsWith('.tsx')) {
        const stats = await fs.promises.stat(fullPath)
        files.push({
          filePath: relativePath.replace(/\\/g, '/'), // Normalize path separators
          lastModified: stats.mtime.toISOString()
        })
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }
  
  return files
}
