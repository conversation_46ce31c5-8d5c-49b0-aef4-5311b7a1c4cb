import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import { ContentManager } from '../content-manager'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { fieldId, newValue, pagePath } = body

    // Validate required fields
    if (!fieldId || !newValue || !pagePath) {
      return NextResponse.json(
        { error: 'Missing required fields: fieldId, newValue, pagePath' },
        { status: 400 }
      )
    }

    console.log('Content update request:', {
      fieldId,
      newValue,
      pagePath,
      timestamp: new Date().toISOString(),
      user: session.user.email
    })

    try {
      // Use the ContentManager to update the content
      const result = await ContentManager.updateContent({
        fieldId,
        newValue,
        userId: session.user.id?.toString() || session.user.email || '',
        pagePath
      })

      if (result.success) {
        return NextResponse.json({
          success: true,
          message: result.message,
          data: {
            fieldId,
            newValue,
            pagePath,
            updatedAt: new Date().toISOString()
          }
        })
      } else {
        return NextResponse.json(
          { error: result.message },
          { status: 400 }
        )
      }

    } catch (error) {
      console.error('Error updating content:', error)
      return NextResponse.json(
        { error: `Failed to update content: ${error instanceof Error ? error.message : 'Unknown error'}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error updating content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Optional: Add GET method to retrieve current content
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const elementId = searchParams.get('elementId')
    const filePath = searchParams.get('filePath')

    if (!elementId || !filePath) {
      return NextResponse.json(
        { error: 'Missing required parameters: elementId, filePath' },
        { status: 400 }
      )
    }

    // For now, return mock data
    // In a real implementation, you would read the actual file content
    return NextResponse.json({
      success: true,
      data: {
        elementId,
        currentText: 'Sample text content',
        filePath,
        lastModified: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error retrieving content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 