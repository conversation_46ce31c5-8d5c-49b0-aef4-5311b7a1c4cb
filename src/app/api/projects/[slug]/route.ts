import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  ApiError
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: { slug: string }
}

// GET /api/projects/[slug] - Get a specific public project by slug
export const GET = with<PERSON>rror<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  const { slug } = await params

  const project = await prisma.projectss.findFirst({
    where: {
      slug,
      ispublic: true,
      status: 'COMPLETED'
    },
    include: {
      client: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
        },
      },
      order: {
        select: {
          id: true,
          ordernumber: true,
          totalamount: true,
          createdat: true,
        },
      },
      services: {
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
        },
      },
      technologies: {
        select: {
          id: true,
          name: true,
          description: true,
          iconurl: true,
        },
      },
      feedbacks: {
        where: {
          ispublic: true,
        },
        select: {
          id: true,
          rating: true,
          comment: true,
          createdat: true,
          client: {
            select: {
              companyname: true,
              contactname: true,
            },
          },
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      documents: {
        select: {
          id: true,
          fileName: true,
          description: true,
          fileSize: true,
          mimeType: true,
          createdat: true,
        },
        take: 5, // Limit to 5 documents for performance
      },
      _count: {
        select: {
          feedbacks: {
            where: {
              ispublic: true,
            },
          },
          tasks: true,
          documents: true,
        },
      },
    },
  })

  if (!project) {
    throw new ApiError('Project not found', 404)
  }

  // Add computed fields
  const enrichedProject = {
    ...project,
    averageRating: project.feedbacks.length > 0 
      ? project.feedbacks.reduce((sum, feedback) => sum + feedback.rating, 0) / project.feedbacks.length
      : null,
    tagsArray: project.tags ? project.tags.split(',').map(tag => tag.trim()) : [],
    duration: project.projstartdate && project.projcompletiondate 
      ? Math.ceil((new Date(project.projcompletiondate).getTime() - new Date(project.projstartdate).getTime()) / (1000 * 60 * 60 * 24))
      : null,
  }

  return successResponse(enrichedProject)
})
