import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

const prisma = new PrismaClient()

const quotationSchema = z.object({
  clientId: z.string(),
  serviceId: z.string(),
  serviceName: z.string(),
  selectedOptions: z.array(z.string()).default([]),
  selectedFeatures: z.array(z.string()).default([]),
  budget: z.number(),
  timeline: z.string(),
  description: z.string(),
  contactPreference: z.enum(['email', 'phone', 'meeting']),
  urgency: z.enum(['low', 'medium', 'high']),
  estimatedCost: z.number().optional(),
  serviceDetails: z.any().optional()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = quotationSchema.parse(body)

    // Create quotation request
    const quotation = await prisma.quotationrequests.create({
      data: {
        clientid: BigInt(validatedData.clientId),
        serviceid: validatedData.serviceId,
        servicename: validatedData.serviceName,
        selectedoptions: validatedData.selectedOptions,
        selectedfeatures: validatedData.selectedFeatures,
        budget: validatedData.budget,
        timeline: validatedData.timeline,
        description: validatedData.description,
        contactpreference: validatedData.contactPreference,
        urgency: validatedData.urgency,
        estimatedcost: validatedData.estimatedCost || null,
        servicedetails: validatedData.serviceDetails || null,
        status: 'pending',
        requestdate: new Date()
      }
    })

    // Convert BigInt fields to strings for JSON serialization
    const serializedQuotation = {
      ...quotation,
      id: quotation.id.toString(),
      clientid: quotation.clientid.toString(),
      budget: quotation.budget.toString(),
      estimatedcost: quotation.estimatedcost?.toString() || null
    }

    return NextResponse.json({
      success: true,
      message: 'Quotation request submitted successfully',
      data: serializedQuotation
    })

  } catch (error) {
    console.error('Quotation creation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, message: 'Invalid request data', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, message: 'Failed to submit quotation request' },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('clientId')

    let whereClause = {}

    if (clientId) {
      whereClause = { clientid: BigInt(clientId) }
    }

    const quotations = await prisma.quotationrequests.findMany({
      where: whereClause,
      orderBy: { requestdate: 'desc' },
      include: {
        client: {
          select: {
            companyname: true,
            contactname: true,
            contactemail: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: quotations
    })

  } catch (error) {
    console.error('Quotations fetch error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch quotations' },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
