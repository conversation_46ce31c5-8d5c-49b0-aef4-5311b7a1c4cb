import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  errorResponse,
  validateRequest,
  validateMethod,
  requireAdmin,
  ApiError
} from '@/services/api/api-utils'
import { updateServiceSchema } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteContext {
  params: {
    id: string
  }
}

// GET /api/services/[id] - Get a specific active service (public endpoint)
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  const { id } = params

  const service = await prisma.services.findFirst({
    where: {
      id,
      isactive: true // Only show active services for public access
    },
    include: {
      category: {
        select: {
          id: true,
          name: true,
          description: true,
        },
      },
      serviceOptions: {
        where: { isactive: true },
        include: {
          features: true,
        },
        orderBy: {
          price: 'asc',
        },
      },
      projects: {
        where: {
          status: { in: ['COMPLETED', 'IN_PROGRESS'] } // Only show relevant projects
        },
        select: {
          id: true,
          name: true,
          status: true,
          imageurl: true,
          projecturl: true,
          client: {
            select: {
              id: true,
              companyname: true,
            },
          },
        },
        take: 3, // Limit to recent projects for showcase
        orderBy: {
          createdat: 'desc',
        },
      },
      _count: {
        select: {
          projects: true,
          orderDetails: true,
          serviceOptions: true,
        },
      },
    },
  })

  if (!service) {
    throw new ApiError('Service not found', 404, 'NOT_FOUND')
  }

  return successResponse(service)
})

// PUT /api/services/[id] - Update a specific service
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const { id } = params
  const validate = validateRequest(updateServiceSchema)
  const data = await validate(request)

  // Check if service exists
  const existingService = await prisma.services.findUnique({
    where: { id },
  })

  if (!existingService) {
    throw new ApiError('Service not found', 404, 'NOT_FOUND')
  }

  // If categoryId is being updated, check if the new category exists
  if (data.categoryId) {
    const category = await prisma.categories.findUnique({
      where: { id: data.categoryId },
    })

    if (!category) {
      throw new ApiError('Category not found', 404, 'CATEGORY_NOT_FOUND')
    }
  }

  const updatedService = await prisma.services.update({
    where: { id },
    data,
    include: {
      category: {
        select: {
          id: true,
          name: true,
          description: true,
        },
      },
      serviceOptions: {
        include: {
          features: true,
        },
      },
      _count: {
        select: {
          projects: true,
          orderDetails: true,
          serviceOptions: true,
        },
      },
    },
  })

  return successResponse(updatedService, 'Service updated successfully')
})

// DELETE /api/services/[id] - Delete a specific service
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const { id } = params

  // Check if service exists
  const existingService = await prisma.services.findUnique({
    where: { id },
    include: {
      projects: { select: { id: true } },
      orderDetails: { select: { id: true } },
      serviceOptions: { select: { id: true } },
    },
  })

  if (!existingService) {
    throw new ApiError('Service not found', 404, 'NOT_FOUND')
  }

  // Check if service is being used
  if (existingService.projects.length > 0) {
    throw new ApiError(
      'Cannot delete service that is used in projects. Please remove it from projects first.',
      400,
      'SERVICE_IN_USE'
    )
  }

  if (existingService.orderDetails.length > 0) {
    throw new ApiError(
      'Cannot delete service that is used in orders. Please remove it from orders first.',
      400,
      'SERVICE_IN_USE'
    )
  }

  // Delete service options first (cascade should handle this, but being explicit)
  if (existingService.serviceOptions.length > 0) {
    await prisma.servicesOption.deleteMany({
      where: { serviceId: id },
    })
  }

  // Delete the service
  await prisma.services.delete({
    where: { id },
  })

  return successResponse(null, 'Service deleted successfully')
})

// PATCH /api/services/[id] - Partial update (toggle active status, etc.)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['PATCH'])
  
  const { id } = params
  const body = await request.json()

  // Check if service exists
  const existingService = await prisma.services.findUnique({
    where: { id },
  })

  if (!existingService) {
    throw new ApiError('Service not found', 404, 'NOT_FOUND')
  }

  // Handle specific patch operations
  const allowedFields = ['isactive', 'displayorder', 'isfeatured']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (field in body) {
      updateData[field] = body[field]
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400, 'NO_VALID_FIELDS')
  }

  const updatedService = await prisma.services.update({
    where: { id },
    data: updateData,
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  return successResponse(updatedService, 'Service updated successfully')
})
