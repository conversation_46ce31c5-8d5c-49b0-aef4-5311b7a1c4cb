import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/services/api/api-utils'
import { createTeamMemberSchema, updateTeamMemberSchema } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/team - List all team members with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, [
      'firstname', 
      'lastname', 
      'position', 
      'department', 
      'email'
    ]))
  }
  
  // Add filter for active/inactive team members
  if (filter === 'active') {
    where.isactive = true
  } else if (filter === 'inactive') {
    where.isactive = false
  }

  // Get total count for pagination
  const total = await prisma.teammembers.count({ where })

  // Get team members with pagination
  const teamMembers = await prisma.teammembers.findMany({
    where,
    include: {
      tasks: {
        select: {
          id: true,
          taskdesc: true,
          status: true,
          projects: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
        },
        orderBy: {
          createdat: 'desc',
        },
        take: 5, // Limit to recent tasks
      },
      _count: {
        select: {
          tasks: true,
          payrollrecords: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  // Transform the data for frontend
  const transformedTeamMembers = teamMembers.map(member => transformFromDbFields.teamMember(member))

  return paginatedResponse(transformedTeamMembers, page, limit, total)
})

// POST /api/team - Create a new team member
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createTeamMemberSchema)
  const data = await validate(request)

  // Check if a team member with the same email already exists
  if (data.email) {
    const existingMember = await prisma.teammembers.findFirst({
      where: {
        email: data.email,
      },
    })

    if (existingMember) {
      throw new Error('A team member with this email already exists')
    }
  }

  const teamMember = await prisma.teammembers.create({
    data,
    include: {
      _count: {
        select: {
          tasks: true,
          payrollrecords: true,
        },
      },
    },
  })

  return successResponse(teamMember, 'Team member created successfully', 201)
})

// PUT /api/team - Bulk update team members (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid team member IDs provided')
  }

  const validate = validateRequest(updateTeamMemberSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedMembers = await prisma.teammembers.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedMembers.count },
    `${updatedMembers.count} team members updated successfully`
  )
})

// DELETE /api/team - Bulk delete team members (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid team member IDs provided')
  }

  // Check if any team members have associated data that should be preserved
  const membersWithData = await prisma.teammembers.findMany({
    where: {
      id: { in: ids },
      OR: [
        { tasks: { some: {} } },
        { payrollrecords: { some: {} } },
      ],
    },
    select: { id: true, firstname: true, lastname: true },
  })

  if (membersWithData.length > 0) {
    const memberNames = membersWithData.map(m => `${m.firstname} ${m.lastname}`).join(', ')
    throw new Error(
      `Cannot delete team members with associated data: ${memberNames}. Please handle their tasks and payroll records first.`
    )
  }

  const deletedMembers = await prisma.teammembers.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedMembers.count },
    `${deletedMembers.count} team members deleted successfully`
  )
})
