import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON>rror<PERSON>andler, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSortQuery
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/testimonials - List all public testimonials with pagination
export const GET = withError<PERSON>and<PERSON>(async (request: NextRequest) => {
  const { page, limit, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause for public testimonials only
  const where: any = {}
  
  // Add filter for featured testimonials
  if (filter === 'featured') {
    where.isfeatured = true
  }

  // Get total count for pagination
  const total = await prisma.testimonials.count({ where })

  // Get testimonials with pagination
  const testimonials = await prisma.testimonials.findMany({
    where,
    select: {
      id: true,
      clientname: true,
      clienttitle: true,
      clientcompany: true,
      clientphotourl: true,
      content: true,
      rating: true,
      isfeatured: true,
      displayorder: true,
      createdat: true,
    },
    orderBy: buildSortQuery(sortBy, sortOrder, { 
      defaultSort: 'displayorder',
      defaultOrder: 'asc'
    }),
    skip,
    take,
  })

  // Transform the data for frontend
  const transformedTestimonials = testimonials.map(testimonial => transformFromDbFields.testimonial(testimonial))

  return paginatedResponse(transformedTestimonials, page, limit, total)
})
