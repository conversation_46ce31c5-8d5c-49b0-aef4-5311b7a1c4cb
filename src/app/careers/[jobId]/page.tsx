import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import JobDetailsClient from './job-details-client';

async function fetchJob(jobId: string) {
  let url = '';
  if (typeof window === 'undefined') {
    // Server-side: build absolute URL
    const headersList = await headers();
    const host = headersList.get('host');
    const protocol = headersList.get('x-forwarded-proto') || 'http';
    url = `${protocol}://${host}/api/careers/${jobId}`;
  } else {
    // Client-side
    url = `/api/careers/${jobId}`;
  }

  const res = await fetch(url, { cache: 'no-store' });
  if (!res.ok) return null;
  return res.json();
}

export default async function JobDetailsPage({ params }: { params: { jobId: string } }) {
  const job = await fetchJob(params.jobId);
  if (!job) return notFound();
  return <JobDetailsClient job={job} />;
}
