'use client'

import React from 'react'
import { safeToLocaleDateString } from '@/lib/utils/date-utils'

interface MessageBubbleProps {
  message: {
    id: string | number
    name: string
    email: string
    message: string
    messageType: string
    contentType: string
    createdAt: string
    isRead?: boolean
    attachments?: Array<{
      id: string
      filename: string
      size: number
      mimeType: string
      url: string
    }>
    sender?: {
      id: string | number
      email: string
      firstname?: string
      lastname?: string
      imageurl?: string
      role: string
    }
    receiver?: {
      id: string | number
      email: string
      firstname?: string
      lastname?: string
      imageurl?: string
      role: string
    }
    parent?: {
      id: string | number
      subject: string
      message: string
      createdAt: string
    }
  }
  currentUserId: string | number
  isSelected?: boolean
  onSelect?: () => void
  showReplyContext?: boolean
}

export function MessageBubble({
  message,
  currentUserId,
  isSelected = false,
  onSelect,
  showReplyContext = true
}: MessageBubbleProps) {
  const isOwnMessage = message.sender?.id?.toString() === currentUserId?.toString()
  const senderName = message.sender 
    ? `${message.sender.firstname || ''} ${message.sender.lastname || ''}`.trim() || message.sender.email
    : message.name

  const getAvatarInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatMessageContent = (content: string, contentType: string) => {
    if (contentType === 'html') {
      return <div dangerouslySetInnerHTML={{ __html: content }} />
    }
    return <p className="whitespace-pre-wrap">{content}</p>
  }

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'contact':
        return (
          <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        )
      case 'reply':
        return (
          <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
          </svg>
        )
      case 'chat':
        return (
          <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        )
      default:
        return null
    }
  }

  return (
    <div 
      className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-4 group ${
        isSelected ? 'bg-blue-50 -mx-2 px-2 py-1 rounded' : ''
      } ${onSelect ? 'cursor-pointer' : ''}`}
      onClick={onSelect}
    >
      <div className={`flex ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'} items-start space-x-2 max-w-[80%]`}>
        {/* Avatar */}
        <div className="flex-shrink-0">
          {message.sender?.imageurl ? (
            <img
              src={message.sender.imageurl}
              alt={senderName}
              className="h-8 w-8 rounded-full object-cover"
            />
          ) : (
            <div className={`h-8 w-8 rounded-full flex items-center justify-center text-xs font-medium text-white ${
              isOwnMessage ? 'bg-blue-500' : 'bg-gray-500'
            }`}>
              {getAvatarInitials(senderName)}
            </div>
          )}
        </div>

        {/* Message Content */}
        <div className={`flex flex-col ${isOwnMessage ? 'items-end' : 'items-start'}`}>
          {/* Sender Info */}
          <div className={`flex items-center space-x-1 mb-1 ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <span className="text-xs font-medium text-gray-700">{senderName}</span>
            <div className="flex items-center space-x-1 text-gray-400">
              {getMessageTypeIcon(message.messageType)}
              <span className="text-xs">{safeToLocaleDateString(message.createdAt)}</span>
              {!message.isRead && !isOwnMessage && (
                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
              )}
            </div>
          </div>

          {/* Reply Context */}
          {showReplyContext && message.parent && (
            <div className={`mb-2 p-2 bg-gray-100 rounded border-l-2 border-gray-300 text-xs max-w-xs ${
              isOwnMessage ? 'border-r-2 border-l-0' : ''
            }`}>
              <p className="text-gray-600 font-medium">Replying to:</p>
              <p className="text-gray-800 truncate">{message.parent.message}</p>
            </div>
          )}

          {/* Message Bubble */}
          <div className={`rounded-lg px-3 py-2 max-w-xs lg:max-w-md ${
            isOwnMessage 
              ? 'bg-blue-500 text-white' 
              : 'bg-white border border-gray-200 text-gray-900'
          }`}>
            <div className="text-sm">
              {formatMessageContent(message.message, message.contentType)}
            </div>

            {/* Attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <div className="mt-2 space-y-1">
                {message.attachments.map((attachment) => (
                  <div key={attachment.id} className={`flex items-center space-x-2 p-1 rounded ${
                    isOwnMessage ? 'bg-blue-400' : 'bg-gray-50'
                  }`}>
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                    </svg>
                    <a
                      href={attachment.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`text-xs hover:underline truncate ${
                        isOwnMessage ? 'text-blue-100' : 'text-blue-600'
                      }`}
                    >
                      {attachment.filename}
                    </a>
                    <span className={`text-xs ${
                      isOwnMessage ? 'text-blue-200' : 'text-gray-500'
                    }`}>
                      ({Math.round(attachment.size / 1024)}KB)
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Message Status */}
          {isOwnMessage && (
            <div className="flex items-center space-x-1 mt-1">
              <svg className="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-xs text-gray-500">Sent</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
