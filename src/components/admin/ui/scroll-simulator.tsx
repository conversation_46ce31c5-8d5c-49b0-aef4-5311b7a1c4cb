'use client'

import React, { useRef, useCallback, useEffect, useState } from 'react'
import { ChevronUpIcon, ChevronDownIcon, CursorArrowRaysIcon } from '@heroicons/react/24/outline'

interface ScrollSimulatorProps {
  containerRef: React.RefObject<HTMLElement>
  className?: string
  showMouseIcon?: boolean
}

export function ScrollSimulator({ 
  containerRef, 
  className = '',
  showMouseIcon = true 
}: ScrollSimulatorProps) {
  const [isScrolling, setIsScrolling] = useState(false)
  const [canScrollUp, setCanScrollUp] = useState(false)
  const [canScrollDown, setCanScrollDown] = useState(false)
  const scrollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const animationFrameRef = useRef<number | null>(null)

  // Check scroll availability
  const checkScrollAvailability = useCallback(() => {
    if (!containerRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current
    setCanScrollUp(scrollTop > 0)
    setCanScrollDown(scrollTop < scrollHeight - clientHeight)
  }, [containerRef])

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    checkScrollAvailability()
    container.addEventListener('scroll', checkScrollAvailability)
    window.addEventListener('resize', checkScrollAvailability)

    return () => {
      container.removeEventListener('scroll', checkScrollAvailability)
      window.removeEventListener('resize', checkScrollAvailability)
    }
  }, [checkScrollAvailability])

  // Simulate smooth mouse wheel scrolling
  const simulateMouseScroll = useCallback((direction: 'up' | 'down', intensity: number = 1) => {
    if (!containerRef.current) return

    const container = containerRef.current
    const scrollAmount = 40 * intensity // Base scroll amount per "wheel tick"
    const duration = 150 // Duration for smooth animation
    const steps = 8 // Number of animation steps
    const stepAmount = scrollAmount / steps
    const stepDuration = duration / steps

    let currentStep = 0
    const targetScrollTop = direction === 'down' 
      ? container.scrollTop + scrollAmount
      : container.scrollTop - scrollAmount

    const animate = () => {
      if (currentStep >= steps || !containerRef.current) return

      const progress = currentStep / steps
      const easeProgress = 1 - Math.pow(1 - progress, 3) // Ease-out cubic

      const newScrollTop = direction === 'down'
        ? container.scrollTop + (stepAmount * easeProgress)
        : container.scrollTop - (stepAmount * easeProgress)

      container.scrollTop = Math.max(0, Math.min(
        newScrollTop,
        container.scrollHeight - container.clientHeight
      ))

      currentStep++
      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animate()
  }, [containerRef])

  // Continuous scrolling while holding button
  const startContinuousScroll = useCallback((direction: 'up' | 'down') => {
    if (isScrolling) return

    setIsScrolling(true)
    
    // Initial scroll
    simulateMouseScroll(direction, 1)
    
    // Start continuous scrolling after a delay
    setTimeout(() => {
      scrollIntervalRef.current = setInterval(() => {
        simulateMouseScroll(direction, 0.8)
      }, 100)
    }, 300)
  }, [isScrolling, simulateMouseScroll])

  const stopContinuousScroll = useCallback(() => {
    setIsScrolling(false)
    
    if (scrollIntervalRef.current) {
      clearInterval(scrollIntervalRef.current)
      scrollIntervalRef.current = null
    }
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
      animationFrameRef.current = null
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopContinuousScroll()
    }
  }, [stopContinuousScroll])

  // Quick scroll to top/bottom
  const scrollToTop = useCallback(() => {
    if (!containerRef.current) return
    containerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
  }, [containerRef])

  const scrollToBottom = useCallback(() => {
    if (!containerRef.current) return
    containerRef.current.scrollTo({ 
      top: containerRef.current.scrollHeight, 
      behavior: 'smooth' 
    })
  }, [containerRef])

  if (!canScrollUp && !canScrollDown) return null

  return (
    <div className={`fixed right-6 top-1/2 transform -translate-y-1/2 z-50 ${className}`}>
      <div className="flex flex-col space-y-2 bg-white rounded-lg shadow-lg border border-gray-200 p-2">
        {/* Mouse Icon */}
        {showMouseIcon && (
          <div className="flex justify-center mb-2">
            <CursorArrowRaysIcon className="h-4 w-4 text-gray-400" />
          </div>
        )}

        {/* Scroll Up Button */}
        <button
          type="button"
          onMouseDown={() => canScrollUp && startContinuousScroll('up')}
          onMouseUp={stopContinuousScroll}
          onMouseLeave={stopContinuousScroll}
          onTouchStart={() => canScrollUp && startContinuousScroll('up')}
          onTouchEnd={stopContinuousScroll}
          onClick={scrollToTop}
          disabled={!canScrollUp}
          className={`p-2 rounded-md transition-all duration-200 ${
            canScrollUp
              ? 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="Scroll up (Click to go to top)"
        >
          <ChevronUpIcon className="h-4 w-4" />
        </button>

        {/* Scroll Down Button */}
        <button
          type="button"
          onMouseDown={() => canScrollDown && startContinuousScroll('down')}
          onMouseUp={stopContinuousScroll}
          onMouseLeave={stopContinuousScroll}
          onTouchStart={() => canScrollDown && startContinuousScroll('down')}
          onTouchEnd={stopContinuousScroll}
          onClick={scrollToBottom}
          disabled={!canScrollDown}
          className={`p-2 rounded-md transition-all duration-200 ${
            canScrollDown
              ? 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="Scroll down (Click to go to bottom)"
        >
          <ChevronDownIcon className="h-4 w-4" />
        </button>

        {/* Scroll Indicator */}
        <div className="text-xs text-gray-500 text-center mt-2 px-1">
          Hold to scroll
        </div>
      </div>
    </div>
  )
}
