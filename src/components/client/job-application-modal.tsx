'use client'

import {
    BriefcaseIcon,
    CheckCircleIcon,
    DocumentArrowUpIcon,
    EnvelopeIcon,
    ExclamationTriangleIcon,
    GlobeAltIcon,
    MapPinIcon,
    PhoneIcon,
    UserIcon,
    XMarkIcon
} from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

const applicationSchema = z.object({
  // Personal Information
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits').regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number'),

  // Location Information
  location: z.string().min(1, 'Current location is required'),
  country: z.string().min(1, 'Country is required'),

  // Professional Information
  currentPosition: z.string().min(1, 'Current position is required'),
  currentCompany: z.string().min(1, 'Current company is required'),
  yearsOfExperience: z.string().min(1, 'Years of experience is required'),
  expectedSalary: z.string().optional(),
  availabilityDate: z.string().min(1, 'Availability date is required'),

  // Work Authorization
  workAuthorization: z.enum(['citizen', 'permanent_resident', 'work_visa', 'student_visa', 'other'], {
    required_error: 'Work authorization status is required'
  }),
  requiresSponsorship: z.enum(['yes', 'no'], {
    required_error: 'Please specify if you require visa sponsorship'
  }),

  // Remote Work
  remotePreference: z.enum(['remote_only', 'hybrid', 'onsite', 'flexible'], {
    required_error: 'Remote work preference is required'
  }),

  // Documents
  resume: z
    .instanceof(FileList)
    .refine((file) => file && file.length === 1, { message: 'Resume (PDF) is required' })
    .refine((file) => file && file.length === 1 && file[0].type === 'application/pdf', { message: 'Only PDF files are allowed' })
    .refine((file) => file && file.length === 1 && file[0].size <= 5 * 1024 * 1024, { message: 'Resume must be less than 5MB' }),

  coverLetter: z.string().min(100, 'Cover letter must be at least 100 characters').max(2000, 'Cover letter must be less than 2000 characters'),

  // Additional Information
  portfolioUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  linkedinUrl: z.string().url('Please enter a valid LinkedIn URL').optional().or(z.literal('')),
  githubUrl: z.string().url('Please enter a valid GitHub URL').optional().or(z.literal('')),

  // How did you hear about us
  referralSource: z.enum(['job_board', 'company_website', 'linkedin', 'referral', 'social_media', 'other'], {
    required_error: 'Please tell us how you heard about this position'
  }),
  referralDetails: z.string().optional(),

  // Consent
  privacyConsent: z.boolean().refine(val => val === true, {
    message: 'You must agree to the privacy policy'
  }),
  marketingConsent: z.boolean().optional(),
});

type ApplicationFormData = z.infer<typeof applicationSchema>;

interface JobApplicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobId: string | number;
  onSuccess?: () => void;
}

export default function JobApplicationModal({ isOpen, onClose, jobId, onSuccess }: JobApplicationModalProps) {
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
  });

  const onSubmit = async (data: ApplicationFormData) => {
    setLoading(true);
    setSubmitError(null);
    setSuccess(false);
    try {
      const formData = new FormData();

      // Combine first and last name for backward compatibility
      formData.append('name', `${data.firstName} ${data.lastName}`);
      formData.append('email', data.email);
      formData.append('phone', data.phone);
      formData.append('coverletter', data.coverLetter);
      formData.append('resume', data.resume[0]);
      formData.append('jobId', String(jobId));

      // Additional fields for enhanced application
      formData.append('location', data.location);
      formData.append('country', data.country);
      formData.append('currentPosition', data.currentPosition);
      formData.append('currentCompany', data.currentCompany);
      formData.append('yearsOfExperience', data.yearsOfExperience);
      formData.append('expectedSalary', data.expectedSalary || '');
      formData.append('availabilityDate', data.availabilityDate);
      formData.append('workAuthorization', data.workAuthorization);
      formData.append('requiresSponsorship', data.requiresSponsorship);
      formData.append('remotePreference', data.remotePreference);
      formData.append('portfolioUrl', data.portfolioUrl || '');
      formData.append('linkedinUrl', data.linkedinUrl || '');
      formData.append('githubUrl', data.githubUrl || '');
      formData.append('referralSource', data.referralSource);
      formData.append('referralDetails', data.referralDetails || '');

      const res = await fetch('/api/careers/apply', {
        method: 'POST',
        body: formData,
      });
      if (!res.ok) throw new Error('Failed to submit application');
      setSuccess(true);
      reset();
      if (fileInputRef.current) fileInputRef.current.value = '';
      if (onSuccess) onSuccess();
    } catch (err: any) {
      setSubmitError(err.message || 'Failed to submit application');
    } finally {
      setLoading(false);
    }
  };

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  if (!isOpen) return null;

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  const stepTitles = [
    'Personal Information',
    'Professional Background',
    'Work Preferences',
    'Documents & Final Details'
  ];

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" onClick={onClose} />
      <div className="flex items-center justify-center min-h-screen px-4 py-8">
        <div className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-auto" onClick={e => e.stopPropagation()}>
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-t-2xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-2xl font-bold">Apply for this Position</h3>
                <p className="text-blue-100 mt-1">Join our team and shape the future of technology</p>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors p-2 rounded-full hover:bg-white/10"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Progress Bar */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-blue-100">Step {currentStep} of {totalSteps}</span>
                <span className="text-sm text-blue-100">{stepTitles[currentStep - 1]}</span>
              </div>
              <div className="w-full bg-blue-500/30 rounded-full h-2">
                <div
                  className="bg-white rounded-full h-2 transition-all duration-300"
                  style={{ width: `${(currentStep / totalSteps) * 100}%` }}
                />
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            {success ? (
              <div className="text-center py-12">
                <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h4 className="text-2xl font-bold text-gray-900 mb-2">Application Submitted Successfully!</h4>
                <p className="text-gray-600 mb-6">
                  Thank you for your interest in joining our team. We'll review your application and get back to you soon.
                </p>
                <button
                  onClick={onClose}
                  className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  Close
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Step 1: Personal Information */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-6">
                      <UserIcon className="w-6 h-6 text-blue-600 mr-3" />
                      <h4 className="text-xl font-semibold text-gray-900">Personal Information</h4>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                        <input
                          type="text"
                          {...register('firstName')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="Enter your first name"
                        />
                        {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                        <input
                          type="text"
                          {...register('lastName')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="Enter your last name"
                        />
                        {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <div className="relative">
                          <EnvelopeIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                          <input
                            type="email"
                            {...register('email')}
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder="<EMAIL>"
                          />
                        </div>
                        {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                        <div className="relative">
                          <PhoneIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                          <input
                            type="tel"
                            {...register('phone')}
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder="+****************"
                          />
                        </div>
                        {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Current Location *</label>
                        <div className="relative">
                          <MapPinIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                          <input
                            type="text"
                            {...register('location')}
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder="City, State"
                          />
                        </div>
                        {errors.location && <p className="text-red-500 text-sm mt-1">{errors.location.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Country *</label>
                        <select
                          {...register('country')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        >
                          <option value="">Select your country</option>
                          <option value="US">United States</option>
                          <option value="CA">Canada</option>
                          <option value="UK">United Kingdom</option>
                          <option value="DE">Germany</option>
                          <option value="FR">France</option>
                          <option value="AU">Australia</option>
                          <option value="IN">India</option>
                          <option value="other">Other</option>
                        </select>
                        {errors.country && <p className="text-red-500 text-sm mt-1">{errors.country.message}</p>}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Professional Background */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-6">
                      <BriefcaseIcon className="w-6 h-6 text-blue-600 mr-3" />
                      <h4 className="text-xl font-semibold text-gray-900">Professional Background</h4>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Current Position *</label>
                        <input
                          type="text"
                          {...register('currentPosition')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="e.g. Senior Software Engineer"
                        />
                        {errors.currentPosition && <p className="text-red-500 text-sm mt-1">{errors.currentPosition.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Current Company *</label>
                        <input
                          type="text"
                          {...register('currentCompany')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="e.g. Tech Corp Inc."
                        />
                        {errors.currentCompany && <p className="text-red-500 text-sm mt-1">{errors.currentCompany.message}</p>}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Years of Experience *</label>
                        <select
                          {...register('yearsOfExperience')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        >
                          <option value="">Select experience level</option>
                          <option value="0-1">0-1 years</option>
                          <option value="2-3">2-3 years</option>
                          <option value="4-5">4-5 years</option>
                          <option value="6-8">6-8 years</option>
                          <option value="9-12">9-12 years</option>
                          <option value="13+">13+ years</option>
                        </select>
                        {errors.yearsOfExperience && <p className="text-red-500 text-sm mt-1">{errors.yearsOfExperience.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Expected Salary (Optional)</label>
                        <input
                          type="text"
                          {...register('expectedSalary')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="e.g. $80,000 - $100,000"
                        />
                        {errors.expectedSalary && <p className="text-red-500 text-sm mt-1">{errors.expectedSalary.message}</p>}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Availability Date *</label>
                      <input
                        type="date"
                        {...register('availabilityDate')}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      />
                      {errors.availabilityDate && <p className="text-red-500 text-sm mt-1">{errors.availabilityDate.message}</p>}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Portfolio URL (Optional)</label>
                        <input
                          type="url"
                          {...register('portfolioUrl')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="https://yourportfolio.com"
                        />
                        {errors.portfolioUrl && <p className="text-red-500 text-sm mt-1">{errors.portfolioUrl.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">LinkedIn URL (Optional)</label>
                        <input
                          type="url"
                          {...register('linkedinUrl')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="https://linkedin.com/in/yourprofile"
                        />
                        {errors.linkedinUrl && <p className="text-red-500 text-sm mt-1">{errors.linkedinUrl.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">GitHub URL (Optional)</label>
                        <input
                          type="url"
                          {...register('githubUrl')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          placeholder="https://github.com/yourusername"
                        />
                        {errors.githubUrl && <p className="text-red-500 text-sm mt-1">{errors.githubUrl.message}</p>}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3: Work Preferences */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-6">
                      <GlobeAltIcon className="w-6 h-6 text-blue-600 mr-3" />
                      <h4 className="text-xl font-semibold text-gray-900">Work Preferences</h4>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Work Authorization Status *</label>
                        <select
                          {...register('workAuthorization')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        >
                          <option value="">Select authorization status</option>
                          <option value="citizen">US Citizen</option>
                          <option value="permanent_resident">Permanent Resident</option>
                          <option value="work_visa">Work Visa (H1B, L1, etc.)</option>
                          <option value="student_visa">Student Visa (F1, OPT)</option>
                          <option value="other">Other</option>
                        </select>
                        {errors.workAuthorization && <p className="text-red-500 text-sm mt-1">{errors.workAuthorization.message}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Require Visa Sponsorship? *</label>
                        <select
                          {...register('requiresSponsorship')}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        >
                          <option value="">Select option</option>
                          <option value="no">No</option>
                          <option value="yes">Yes</option>
                        </select>
                        {errors.requiresSponsorship && <p className="text-red-500 text-sm mt-1">{errors.requiresSponsorship.message}</p>}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Remote Work Preference *</label>
                      <select
                        {...register('remotePreference')}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      >
                        <option value="">Select preference</option>
                        <option value="remote_only">Remote Only</option>
                        <option value="hybrid">Hybrid (Remote + Office)</option>
                        <option value="onsite">On-site Only</option>
                        <option value="flexible">Flexible</option>
                      </select>
                      {errors.remotePreference && <p className="text-red-500 text-sm mt-1">{errors.remotePreference.message}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">How did you hear about this position? *</label>
                      <select
                        {...register('referralSource')}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      >
                        <option value="">Select source</option>
                        <option value="job_board">Job Board (Indeed, LinkedIn, etc.)</option>
                        <option value="company_website">Company Website</option>
                        <option value="linkedin">LinkedIn</option>
                        <option value="referral">Employee Referral</option>
                        <option value="social_media">Social Media</option>
                        <option value="other">Other</option>
                      </select>
                      {errors.referralSource && <p className="text-red-500 text-sm mt-1">{errors.referralSource.message}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Additional Details (Optional)</label>
                      <textarea
                        {...register('referralDetails')}
                        rows={3}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder="If you selected 'Employee Referral' or 'Other', please provide details..."
                      />
                      {errors.referralDetails && <p className="text-red-500 text-sm mt-1">{errors.referralDetails.message}</p>}
                    </div>
                  </div>
                )}

                {/* Step 4: Documents & Final Details */}
                {currentStep === 4 && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-6">
                      <DocumentArrowUpIcon className="w-6 h-6 text-blue-600 mr-3" />
                      <h4 className="text-xl font-semibold text-gray-900">Documents & Final Details</h4>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Resume (PDF) *</label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                        <DocumentArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <Controller
                          name="resume"
                          control={control}
                          defaultValue={undefined}
                          render={({ field }) => (
                            <div>
                              <input
                                type="file"
                                accept="application/pdf"
                                onChange={e => field.onChange(e.target.files)}
                                ref={fileInputRef}
                                className="hidden"
                                id="resume-upload"
                              />
                              <label
                                htmlFor="resume-upload"
                                className="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                              >
                                Choose PDF File
                              </label>
                              <p className="text-sm text-gray-500 mt-2">Maximum file size: 5MB</p>
                            </div>
                          )}
                        />
                        {errors.resume && <p className="text-red-500 text-sm mt-2">{errors.resume.message}</p>}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Cover Letter *</label>
                      <textarea
                        {...register('coverLetter')}
                        rows={6}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder="Tell us why you're interested in this position and what makes you a great fit for our team..."
                      />
                      <div className="flex justify-between text-sm text-gray-500 mt-1">
                        <span>Minimum 100 characters</span>
                        <span>Maximum 2000 characters</span>
                      </div>
                      {errors.coverLetter && <p className="text-red-500 text-sm mt-1">{errors.coverLetter.message}</p>}
                    </div>

                    {/* Consent Checkboxes */}
                    <div className="space-y-4 pt-6 border-t border-gray-200">
                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          {...register('privacyConsent')}
                          className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label className="ml-3 text-sm text-gray-700">
                          I agree to the <a href="/privacy" target="_blank" className="text-blue-600 hover:underline">Privacy Policy</a> and consent to the processing of my personal data for recruitment purposes. *
                        </label>
                      </div>
                      {errors.privacyConsent && <p className="text-red-500 text-sm">{errors.privacyConsent.message}</p>}

                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          {...register('marketingConsent')}
                          className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label className="ml-3 text-sm text-gray-700">
                          I would like to receive updates about future job opportunities and company news. (Optional)
                        </label>
                      </div>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start">
                        <CheckCircleIcon className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                        <div className="text-sm text-blue-800">
                          <p className="font-medium mb-1">Ready to submit your application?</p>
                          <p>Please review all information before submitting. We'll get back to you within 5-7 business days.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  {currentStep < totalSteps ? (
                    <button
                      type="button"
                      onClick={nextStep}
                      className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Next Step
                    </button>
                  ) : (
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Submitting...
                        </div>
                      ) : (
                        'Submit Application'
                      )}
                    </button>
                  )}
                </div>

                {submitError && (
                  <div className="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
                    <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
                    {submitError}
                  </div>
                )}
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
