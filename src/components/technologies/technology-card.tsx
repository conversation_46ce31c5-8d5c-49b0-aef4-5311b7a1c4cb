'use client';

import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { StarIcon, ArrowTopRightOnSquareIcon } from '@heroicons/react/24/outline';
import { Technology } from '@/types/shared/technology';

interface TechnologyCardProps {
  technology: Technology;
  index: number;
  getProficiencyColor: (level: string) => string;
}

export function TechnologyCard({ technology, index, getProficiencyColor }: TechnologyCardProps) {
  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="group bg-white p-6 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-300"
    >
      <header className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Image
              src={technology.logo}
              alt={`${technology.name} logo`}
              width={40}
              height={40}
              className="w-10 h-10 object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">{technology.name}</h3>
            <p className="text-sm text-gray-500">{technology.category}</p>
          </div>
        </div>
        {technology.isFeatured && (
          <StarIcon 
            className="w-5 h-5 text-yellow-400 fill-current" 
            aria-label="Featured technology"
          />
        )}
      </header>

      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
        {technology.description}
      </p>

      <div className="space-y-2 mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Proficiency</span>
          <span 
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(technology.proficiencyLevel)}`}
            aria-label={`Proficiency level: ${technology.proficiencyLevel}`}
          >
            {technology.proficiencyLevel}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Experience</span>
          <span className="text-sm font-medium text-gray-900">
            {technology.yearsOfExperience} years
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Projects</span>
          <span className="text-sm font-medium text-gray-900">
            {technology.projectsUsed}
          </span>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex flex-wrap gap-1" role="list" aria-label="Technology tags">
          {technology.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
              role="listitem"
            >
              {tag}
            </span>
          ))}
          {technology.tags.length > 3 && (
            <span 
              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
              role="listitem"
              aria-label={`${technology.tags.length - 3} more tags`}
            >
              +{technology.tags.length - 3}
            </span>
          )}
        </div>
      </div>

      <footer className="flex justify-between items-center">
        <Link
          href={`/technologies/${technology.id}`}
          className="text-blue-600 hover:text-blue-700 font-medium text-sm group/link focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
          aria-label={`Learn more about ${technology.name}`}
        >
          Learn More
          <ArrowTopRightOnSquareIcon className="ml-1 h-4 w-4 inline transition-transform group-hover/link:translate-x-0.5 group-hover/link:-translate-y-0.5" />
        </Link>
        <a
          href={technology.website}
          target="_blank"
          rel="noopener noreferrer"
          className="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded"
          title={`Visit ${technology.name} official website`}
          aria-label={`Visit ${technology.name} official website (opens in new tab)`}
        >
          <ArrowTopRightOnSquareIcon className="h-4 w-4" />
        </a>
      </footer>
    </motion.article>
  );
} 