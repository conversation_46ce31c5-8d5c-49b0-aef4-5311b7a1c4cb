// Google Analytics configuration and utilities

declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID

// Initialize Google Analytics
export const initGA = () => {
  if (!GA_TRACKING_ID) {
    console.warn('Google Analytics ID not found. Analytics will be disabled.')
    return
  }

  // Initialize dataLayer
  window.dataLayer = window.dataLayer || []
  
  // Define gtag function
  window.gtag = function gtag(...args: any[]) {
    window.dataLayer.push(args)
  }

  // Configure Google Analytics
  window.gtag('js', new Date())
  window.gtag('config', GA_TRACKING_ID, {
    page_title: document.title,
    page_location: window.location.href,
  })
}

// Track page views
export const trackPageView = (url: string, title?: string) => {
  if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

  window.gtag('config', GA_TRACKING_ID, {
    page_title: title || document.title,
    page_location: url,
  })
}

// Track events
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  })
}

// Predefined event tracking functions
export const analytics = {
  // Contact form events
  contactFormSubmit: (formType: string = 'general') => {
    trackEvent('submit', 'contact_form', formType)
  },

  contactFormError: (error: string) => {
    trackEvent('error', 'contact_form', error)
  },

  // Service interactions
  serviceView: (serviceName: string) => {
    trackEvent('view', 'service', serviceName)
  },

  serviceInquiry: (serviceName: string) => {
    trackEvent('inquiry', 'service', serviceName)
  },

  // Portfolio interactions
  portfolioView: (projectName: string) => {
    trackEvent('view', 'portfolio', projectName)
  },

  portfolioDemo: (projectName: string) => {
    trackEvent('demo_click', 'portfolio', projectName)
  },

  // Blog interactions
  blogPostView: (postTitle: string) => {
    trackEvent('view', 'blog_post', postTitle)
  },

  blogPostShare: (postTitle: string, platform: string) => {
    trackEvent('share', 'blog_post', `${postTitle} - ${platform}`)
  },

  // Download tracking
  fileDownload: (fileName: string, fileType: string) => {
    trackEvent('download', 'file', `${fileName} (${fileType})`)
  },

  // Navigation tracking
  navigationClick: (linkText: string, destination: string) => {
    trackEvent('click', 'navigation', `${linkText} -> ${destination}`)
  },

  // CTA tracking
  ctaClick: (ctaText: string, location: string) => {
    trackEvent('click', 'cta', `${ctaText} (${location})`)
  },

  // Search tracking
  search: (query: string, resultsCount: number) => {
    trackEvent('search', 'site_search', query, resultsCount)
  },

  // Error tracking
  error: (errorType: string, errorMessage: string) => {
    trackEvent('error', errorType, errorMessage)
  },

  // Performance tracking
  performanceMetric: (metricName: string, value: number) => {
    trackEvent('performance', 'metric', metricName, value)
  },

  // User engagement
  timeOnPage: (seconds: number, page: string) => {
    trackEvent('engagement', 'time_on_page', page, seconds)
  },

  scrollDepth: (percentage: number, page: string) => {
    trackEvent('engagement', 'scroll_depth', page, percentage)
  },
}

// Custom hook for analytics in React components
export const useAnalytics = () => {
  return {
    trackPageView,
    trackEvent,
    ...analytics,
  }
}

// Enhanced ecommerce tracking (for future use)
export const ecommerce = {
  // Track purchases
  purchase: (transactionId: string, value: number, currency: string = 'USD', items: any[]) => {
    if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

    window.gtag('event', 'purchase', {
      transaction_id: transactionId,
      value: value,
      currency: currency,
      items: items,
    })
  },

  // Track service inquiries as conversions
  serviceInquiry: (serviceName: string, value: number) => {
    if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

    window.gtag('event', 'conversion', {
      send_to: `${GA_TRACKING_ID}/service_inquiry`,
      event_category: 'lead_generation',
      event_label: serviceName,
      value: value,
    })
  },

  // Track quote requests
  quoteRequest: (serviceType: string, estimatedValue: number) => {
    if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

    window.gtag('event', 'conversion', {
      send_to: `${GA_TRACKING_ID}/quote_request`,
      event_category: 'lead_generation',
      event_label: serviceType,
      value: estimatedValue,
    })
  },
}

// Privacy and consent management
export const privacy = {
  // Grant analytics consent
  grantConsent: () => {
    if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

    window.gtag('consent', 'update', {
      analytics_storage: 'granted',
    })
  },

  // Deny analytics consent
  denyConsent: () => {
    if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

    window.gtag('consent', 'update', {
      analytics_storage: 'denied',
    })
  },

  // Set default consent state
  setDefaultConsent: (granted: boolean = false) => {
    if (!GA_TRACKING_ID || typeof window.gtag === 'undefined') return

    window.gtag('consent', 'default', {
      analytics_storage: granted ? 'granted' : 'denied',
      wait_for_update: 500,
    })
  },
}

// Utility to check if analytics is enabled
export const isAnalyticsEnabled = (): boolean => {
  return !!(GA_TRACKING_ID && typeof window !== 'undefined' && window.gtag)
}

// Debug function for development
export const debugAnalytics = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Analytics Debug Info:', {
      trackingId: GA_TRACKING_ID,
      isEnabled: isAnalyticsEnabled(),
      dataLayer: typeof window !== 'undefined' ? window.dataLayer : 'Not available',
    })
  }
}
