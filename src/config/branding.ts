// Branding and configuration constants
export const BRANDING = {
  company: {
    name: process.env.APP_NAME || 'Technoloway',
    tagline: 'Innovative Software Solutions',
    description: 'We create cutting-edge software solutions that drive business growth and digital transformation.',
    founded: '2020',
    location: 'Global',
  },
  contact: {
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    phone: '+****************',
    address: '123 Tech Street, Innovation City, IC 12345',
    website: process.env.APP_URL || 'https://technoloway.com',
  },
  social: {
    twitter: 'https://twitter.com/technoloway',
    linkedin: 'https://linkedin.com/company/technoloway',
    github: 'https://github.com/technoloway',
    facebook: 'https://facebook.com/technoloway',
    instagram: 'https://instagram.com/technoloway',
  },
  theme: {
    primaryColor: '#3B82F6', // Blue-500
    secondaryColor: '#10B981', // Emerald-500
    accentColor: '#F59E0B', // Amber-500
    darkColor: '#1F2937', // Gray-800
    lightColor: '#F9FAFB', // Gray-50
  },
  seo: {
    defaultTitle: 'Technoloway - Innovative Software Solutions',
    defaultDescription: 'Professional software development services including web applications, mobile apps, and digital transformation solutions.',
    keywords: 'software development, web applications, mobile apps, digital transformation, technology consulting',
    ogImage: '/images/og-image.jpg',
  },
  features: {
    blog: true,
    portfolio: true,
    testimonials: true,
    team: true,
    services: true,
    contact: true,
    newsletter: false,
    chat: false,
  },
}

// Navigation structure
export const NAVIGATION = {
  main: [
    { name: 'Home', href: '/', icon: 'home' },
    { name: 'About', href: '/about', icon: 'info' },
    { name: 'Services', href: '/services', icon: 'cog' },
    { name: 'Portfolio', href: '/portfolio', icon: 'briefcase' },
    { name: 'Team', href: '/team', icon: 'users' },
    { name: 'Blog', href: '/blog', icon: 'document' },
    { name: 'Contact', href: '/contact', icon: 'mail' },
  ],
  footer: [
    {
      title: 'Services',
      links: [
        { name: 'Web Development', href: '/services/web-development' },
        { name: 'Mobile Apps', href: '/services/mobile-apps' },
        { name: 'Consulting', href: '/services/consulting' },
        { name: 'Support', href: '/services/support' },
      ],
    },
    {
      title: 'Company',
      links: [
        { name: 'About Us', href: '/about' },
        { name: 'Our Team', href: '/team' },
        { name: 'Careers', href: '/careers' },
        { name: 'Contact', href: '/contact' },
      ],
    },
    {
      title: 'Resources',
      links: [
        { name: 'Blog', href: '/blog' },
        { name: 'Case Studies', href: '/portfolio' },
        { name: 'Documentation', href: '/docs' },
        { name: 'Support', href: '/support' },
      ],
    },
    {
      title: 'Legal',
      links: [
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'Terms of Service', href: '/terms' },
        { name: 'Cookie Policy', href: '/cookies' },
        { name: 'GDPR', href: '/gdpr' },
      ],
    },
  ],
  admin: [
    { name: 'Dashboard', href: '/admin', icon: 'chart-bar' },
    { name: 'Projects', href: '/admin/projects', icon: 'briefcase' },
    { name: 'Clients', href: '/admin/clients', icon: 'users' },
    { name: 'Services', href: '/admin/services', icon: 'cog' },
    { name: 'Team', href: '/admin/team', icon: 'user-group' },
    { name: 'Blog', href: '/admin/blog', icon: 'document-text' },
    { name: 'Technologies', href: '/admin/technologies', icon: 'code' },
    { name: 'Contact Forms', href: '/admin/contact', icon: 'mail' },
    { name: 'Settings', href: '/admin/settings', icon: 'cog-6-tooth' },
  ],
}

// Content sections
export const CONTENT = {
  hero: {
    title: 'Transform Your Business with Innovative Software Solutions',
    subtitle: 'We build cutting-edge web applications, mobile apps, and digital platforms that drive growth and efficiency.',
    cta: 'Get Started Today',
    ctaSecondary: 'View Our Work',
  },
  about: {
    title: 'About Technoloway',
    subtitle: 'Your Trusted Technology Partner',
    content: `We are a team of passionate developers, designers, and strategists dedicated to creating exceptional digital experiences. With years of experience in software development, we help businesses of all sizes achieve their digital transformation goals.`,
    values: [
      {
        title: 'Innovation',
        description: 'We stay ahead of technology trends to deliver cutting-edge solutions.',
        icon: 'lightbulb',
      },
      {
        title: 'Quality',
        description: 'We maintain the highest standards in code quality and user experience.',
        icon: 'shield-check',
      },
      {
        title: 'Partnership',
        description: 'We work closely with our clients as trusted technology partners.',
        icon: 'handshake',
      },
      {
        title: 'Results',
        description: 'We focus on delivering measurable business outcomes and ROI.',
        icon: 'chart-trending-up',
      },
    ],
  },
  services: {
    title: 'Our Services',
    subtitle: 'Comprehensive Software Development Solutions',
    description: 'From concept to deployment, we provide end-to-end software development services tailored to your business needs.',
  },
  portfolio: {
    title: 'Our Portfolio',
    subtitle: 'Showcasing Our Best Work',
    description: 'Explore our portfolio of successful projects and see how we\'ve helped businesses achieve their digital goals.',
  },
  team: {
    title: 'Meet Our Team',
    subtitle: 'The People Behind the Innovation',
    description: 'Our diverse team of experts brings together years of experience in software development, design, and business strategy.',
  },
  contact: {
    title: 'Get In Touch',
    subtitle: 'Ready to Start Your Project?',
    description: 'Contact us today to discuss your project requirements and learn how we can help bring your vision to life.',
  },
}

// Utility functions
export function getBrandColor(colorName: keyof typeof BRANDING.theme): string {
  return BRANDING.theme[colorName]
}

export function getNavigation(section: keyof typeof NAVIGATION) {
  return NAVIGATION[section]
}

export function getContent(section: keyof typeof CONTENT) {
  return CONTENT[section]
}

export function getSEODefaults() {
  return BRANDING.seo
}

export function getCompanyInfo() {
  return {
    ...BRANDING.company,
    ...BRANDING.contact,
  }
}
