import { Technology } from '@/types/shared/technology';

export const technologies: Technology[] = [
  {
    id: 'react',
    name: 'React',
    description: 'A JavaScript library for building user interfaces with component-based architecture and virtual DOM.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 5,
    projectsUsed: 45,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
    website: 'https://reactjs.org',
    documentation: 'https://reactjs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'UI', 'SPA', 'Component-based'],
    useCases: ['Single Page Applications', 'Interactive UIs', 'Component Libraries', 'Progressive Web Apps'],
    advantages: [
      'Virtual DOM for optimal performance',
      'Large ecosystem and community',
      'Reusable component architecture',
      'Strong developer tools'
    ]
  },
  {
    id: 'nextjs',
    name: 'Next.js',
    description: 'The React framework for production with server-side rendering, static site generation, and full-stack capabilities.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 4,
    projectsUsed: 32,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',
    website: 'https://nextjs.org',
    documentation: 'https://nextjs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['React', 'SSR', 'SSG', 'Full-stack'],
    useCases: ['E-commerce Sites', 'Corporate Websites', 'Blogs', 'Web Applications'],
    advantages: [
      'Built-in SEO optimization',
      'Automatic code splitting',
      'API routes for backend logic',
      'Excellent performance out of the box'
    ]
  },
  {
    id: 'nodejs',
    name: 'Node.js',
    description: 'JavaScript runtime built on Chrome\'s V8 JavaScript engine for scalable server-side development.',
    category: 'Backend',
    type: 'Runtime',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 6,
    projectsUsed: 38,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
    website: 'https://nodejs.org',
    documentation: 'https://nodejs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'Server', 'API', 'Microservices'],
    useCases: ['REST APIs', 'Real-time Applications', 'Microservices', 'Command Line Tools'],
    advantages: [
      'Non-blocking I/O operations',
      'Large package ecosystem (npm)',
      'JavaScript everywhere',
      'High performance for I/O intensive apps'
    ]
  },
  {
    id: 'typescript',
    name: 'TypeScript',
    description: 'Typed superset of JavaScript that compiles to plain JavaScript for better development experience.',
    category: 'Language',
    type: 'Programming Language',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 4,
    projectsUsed: 42,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
    website: 'https://www.typescriptlang.org',
    documentation: 'https://www.typescriptlang.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'Type Safety', 'Development'],
    useCases: ['Large Applications', 'Team Development', 'Enterprise Software', 'Library Development'],
    advantages: [
      'Static type checking',
      'Better IDE support and autocomplete',
      'Easier refactoring and maintenance',
      'Catches errors at compile time'
    ]
  },
  {
    id: 'python',
    name: 'Python',
    description: 'High-level programming language known for its simplicity and versatility in various domains.',
    category: 'Language',
    type: 'Programming Language',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 7,
    projectsUsed: 28,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg',
    website: 'https://www.python.org',
    documentation: 'https://docs.python.org',
    isActive: true,
    isFeatured: false,
    tags: ['AI/ML', 'Data Science', 'Backend', 'Automation'],
    useCases: ['Machine Learning', 'Data Analysis', 'Web Development', 'Automation Scripts'],
    advantages: [
      'Simple and readable syntax',
      'Extensive library ecosystem',
      'Great for rapid prototyping',
      'Strong community support'
    ]
  },
  {
    id: 'postgresql',
    name: 'PostgreSQL',
    description: 'Advanced open-source relational database with strong standards compliance and extensibility.',
    category: 'Database',
    type: 'Database',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 5,
    projectsUsed: 35,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',
    website: 'https://www.postgresql.org',
    documentation: 'https://www.postgresql.org/docs',
    isActive: true,
    isFeatured: false,
    tags: ['SQL', 'ACID', 'Scalable', 'Open Source'],
    useCases: ['Web Applications', 'Data Warehousing', 'Analytics', 'Enterprise Systems'],
    advantages: [
      'ACID compliance and reliability',
      'Advanced SQL features',
      'Extensible with custom functions',
      'Excellent performance and scalability'
    ]
  },
  {
    id: 'aws',
    name: 'AWS',
    description: 'Amazon Web Services cloud computing platform offering a wide range of infrastructure services.',
    category: 'Cloud',
    type: 'Platform',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 4,
    projectsUsed: 25,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg',
    website: 'https://aws.amazon.com',
    documentation: 'https://docs.aws.amazon.com',
    isActive: true,
    isFeatured: true,
    tags: ['Cloud', 'Infrastructure', 'Scalable', 'DevOps'],
    useCases: ['Web Hosting', 'Data Storage', 'Machine Learning', 'Serverless Computing'],
    advantages: [
      'Global infrastructure and availability',
      'Comprehensive service portfolio',
      'Pay-as-you-use pricing model',
      'Enterprise-grade security'
    ]
  },
  {
    id: 'docker',
    name: 'Docker',
    description: 'Platform for developing, shipping, and running applications using containerization technology.',
    category: 'DevOps',
    type: 'Tool',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 3,
    projectsUsed: 30,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg',
    website: 'https://www.docker.com',
    documentation: 'https://docs.docker.com',
    isActive: true,
    isFeatured: false,
    tags: ['Containerization', 'DevOps', 'Deployment'],
    useCases: ['Application Deployment', 'Development Environment', 'Microservices', 'CI/CD'],
    advantages: [
      'Consistent environments across platforms',
      'Lightweight and efficient',
      'Easy scaling and orchestration',
      'Simplified deployment process'
    ]
  }
];

export const categories = ['All', 'Frontend', 'Backend', 'Language', 'Database', 'Cloud', 'DevOps'] as const;
export const proficiencyLevels = ['All', 'Beginner', 'Intermediate', 'Advanced', 'Expert'] as const;

export const technologyStats = [
  { label: 'Technologies Mastered', value: '100+' },
  { label: 'Years Combined Experience', value: '50+' },
  { label: 'Projects Delivered', value: '500+' },
  { label: 'Expert Level Skills', value: '25+' }
]; 