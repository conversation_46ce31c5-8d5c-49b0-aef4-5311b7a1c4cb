import { prisma } from '@/config/prisma'

// Re-export types from services/api
export type { ProjectListItem, ProjectDetails } from '@/services/api/project'

/**
 * Get projects for a specific client
 */
export async function getClientProjects(clientId: string, options: {
  page?: number
  limit?: number
  search?: string
  status?: string
} = {}) {
  const { page = 1, limit = 10, search, status } = options
  const skip = (page - 1) * limit

  const where: any = {
    clientid: parseInt(clientId)
  }
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (status) {
    where.status = status
  }

  const [projects, total] = await Promise.all([
    prisma.projects.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdat: 'desc' },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactemail: true
          }
        },
        projecttechnologies: {
          include: {
            technologies: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        }
      }
    }),
    prisma.projects.count({ where })
  ])

  return { projects, total }
}

/**
 * Get project status options
 */
export function getProjectStatusOptions() {
  return [
    { value: 'PLANNING', label: 'Planning' },
    { value: 'IN_PROGRESS', label: 'In Progress' },
    { value: 'ON_HOLD', label: 'On Hold' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'CANCELLED', label: 'Cancelled' }
  ]
}

/**
 * Get project by ID
 */
export async function getProject(projectId: string) {
  return await prisma.projects.findUnique({
    where: { id: parseInt(projectId) },
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactemail: true,
          contactphone: true
        }
      },

      projectdocuments: {
        select: {
          id: true,
          filename: true,
          fileurl: true,
          filetype: true
        }
      }
    }
  })
}

/**
 * Get all projects with optional pagination and search
 */
export async function getProjects(options: {
  page?: number
  limit?: number
  search?: string
  status?: string
  technology?: string
} = {}) {
  const { page = 1, limit = 10, search, status, technology } = options
  const skip = (page - 1) * limit

  const where: any = {}
  
  if (search) {
    where.OR = [
      { projname: { contains: search, mode: 'insensitive' } },
      { projdescription: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (status) {
    where.projstatus = status
  }

  if (technology) {
    where.projecttechnologies = {
      some: {
        technologies: {
          name: { contains: technology, mode: 'insensitive' }
        }
      }
    }
  }

  const [projects, total] = await Promise.all([
    prisma.projects.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdat: 'desc' },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactemail: true
          }
        },
        projecttechnologies: {
          include: {
            technologies: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        }
      }
    }),
    prisma.projects.count({ where })
  ])

  return { projects, total }
} 