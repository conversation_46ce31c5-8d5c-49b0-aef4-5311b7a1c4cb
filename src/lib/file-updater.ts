import fs from 'fs/promises'
import path from 'path'

export interface FileUpdateRequest {
  filePath: string
  lineNumber: number
  oldText: string
  newText: string
}

export interface FileUpdateResult {
  success: boolean
  message: string
  lineNumber?: number
}

export class FileUpdater {
  /**
   * Update a specific line in a file
   */
  static async updateLine(request: FileUpdateRequest): Promise<FileUpdateResult> {
    try {
      const { filePath, lineNumber, oldText, newText } = request
      
      // Read the file
      const content = await fs.readFile(filePath, 'utf-8')
      const lines = content.split('\n')
      
      // Find the line to update
      let targetLine = lineNumber
      
      // If lineNumber is 1, search for the text in nearby lines
      if (lineNumber === 1) {
        const searchRange = 10 // Search within 10 lines
        for (let i = 0; i < Math.min(searchRange, lines.length); i++) {
          if (lines[i].includes(oldText)) {
            targetLine = i + 1
            break
          }
        }
      }
      
      // Check if the line exists and contains the old text
      if (targetLine > lines.length || !lines[targetLine - 1].includes(oldText)) {
        return {
          success: false,
          message: `Could not find line ${targetLine} with text "${oldText}" in file ${filePath}`
        }
      }
      
      // Update the line
      lines[targetLine - 1] = lines[targetLine - 1].replace(oldText, newText)
      
      // Write the file back
      await fs.writeFile(filePath, lines.join('\n'), 'utf-8')
      
      return {
        success: true,
        message: `Successfully updated line ${targetLine} in ${filePath}`,
        lineNumber: targetLine
      }
    } catch (error) {
      return {
        success: false,
        message: `Error updating file: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Update multiple lines in a file
   */
  static async updateMultipleLines(
    filePath: string, 
    updates: Array<{ lineNumber: number; oldText: string; newText: string }>
  ): Promise<FileUpdateResult> {
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const lines = content.split('\n')
      
      let updatedLines = 0
      
      for (const update of updates) {
        const { lineNumber, oldText, newText } = update
        
        if (lineNumber <= lines.length && lines[lineNumber - 1].includes(oldText)) {
          lines[lineNumber - 1] = lines[lineNumber - 1].replace(oldText, newText)
          updatedLines++
        }
      }
      
      if (updatedLines === 0) {
        return {
          success: false,
          message: 'No lines were updated'
        }
      }
      
      await fs.writeFile(filePath, lines.join('\n'), 'utf-8')
      
      return {
        success: true,
        message: `Successfully updated ${updatedLines} lines in ${filePath}`
      }
    } catch (error) {
      return {
        success: false,
        message: `Error updating file: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Find and replace text in a file
   */
  static async findAndReplace(
    filePath: string, 
    searchText: string, 
    replaceText: string
  ): Promise<FileUpdateResult> {
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      
      if (!content.includes(searchText)) {
        return {
          success: false,
          message: `Could not find text "${searchText}" in file ${filePath}`
        }
      }
      
      const updatedContent = content.replace(new RegExp(searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceText)
      
      await fs.writeFile(filePath, updatedContent, 'utf-8')
      
      return {
        success: true,
        message: `Successfully replaced text in ${filePath}`
      }
    } catch (error) {
      return {
        success: false,
        message: `Error updating file: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Check if a file exists
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get file information
   */
  static async getFileInfo(filePath: string): Promise<{ exists: boolean; size?: number; modified?: Date }> {
    try {
      const stats = await fs.stat(filePath)
      return {
        exists: true,
        size: stats.size,
        modified: stats.mtime
      }
    } catch {
      return {
        exists: false
      }
    }
  }
} 