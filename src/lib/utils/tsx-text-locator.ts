import fs from 'fs/promises'
import path from 'path'

export interface TsxTextLocation {
  filePath: string
  lineNumber: number
  columnStart: number
  columnEnd: number
  fullLine: string
  textContent: string
  context: 'jsx-text' | 'jsx-attribute' | 'string-literal' | 'template-literal' | 'comment' | 'getContent-fallback' | 'getContent-key' | 'unknown'
  confidence: number // 0-100, how confident we are this is the right match
}

export class TsxTextLocator {
  /**
   * Enhanced text detection for TSX files with JSX syntax awareness
   */
  static async findTextInTsxFile(filePath: string, searchText: string): Promise<TsxTextLocation[]> {
    try {
      const absolutePath = path.resolve(process.cwd(), filePath)
      const fileContent = await fs.readFile(absolutePath, 'utf-8')
      const lines = fileContent.split('\n')
      
      const locations: TsxTextLocation[] = []
      
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex]
        const lineNumber = lineIndex + 1
        
        // Find all occurrences of the search text in this line
        const matches = this.findTextInLine(line, searchText, lineNumber, filePath)
        locations.push(...matches)
      }
      
      // Sort by confidence (highest first)
      return locations.sort((a, b) => b.confidence - a.confidence)
      
    } catch (error) {
      console.error('Error finding text in TSX file:', error)
      return []
    }
  }
  
  /**
   * Find text occurrences in a single line with context awareness
   */
  private static findTextInLine(line: string, searchText: string, lineNumber: number, filePath: string): TsxTextLocation[] {
    const locations: TsxTextLocation[] = []
    const trimmedLine = line.trim()

    // Skip empty lines and pure whitespace
    if (!trimmedLine) return locations

    // First, check for getContent() calls - highest priority
    const getContentMatches = this.findGetContentMatches(line, searchText, lineNumber, filePath)
    if (getContentMatches.length > 0) {
      locations.push(...getContentMatches)
    }

    // Then check for regular text matches
    let searchIndex = 0
    while (true) {
      const textIndex = line.indexOf(searchText, searchIndex)
      if (textIndex === -1) break

      // Skip if this match is already covered by a getContent match
      const alreadyCovered = getContentMatches.some(match =>
        textIndex >= match.columnStart - 1 && textIndex < match.columnEnd - 1
      )

      if (!alreadyCovered) {
        const context = this.determineContext(line, textIndex, searchText)
        const confidence = this.calculateConfidence(line, textIndex, searchText, context)

        // Only include matches with reasonable confidence
        if (confidence >= 30) {
          locations.push({
            filePath,
            lineNumber,
            columnStart: textIndex + 1,
            columnEnd: textIndex + searchText.length + 1,
            fullLine: line,
            textContent: searchText,
            context,
            confidence
          })
        }
      }

      searchIndex = textIndex + 1
    }

    return locations
  }

  /**
   * Find text matches within getContent() function calls
   */
  private static findGetContentMatches(line: string, searchText: string, lineNumber: number, filePath: string): TsxTextLocation[] {
    const matches: TsxTextLocation[] = []

    // Enhanced regex to match getContent() calls with various quote styles and spacing
    // Matches: getContent('page', 'section', 'key', 'fallback text')
    const getContentRegex = /getContent\s*\(\s*(['"`])([^'"`]+)\1\s*,\s*(['"`])([^'"`]+)\3\s*,\s*(['"`])([^'"`]+)\5\s*,\s*(['"`])([^'"`]*)\7\s*\)/g

    let match
    while ((match = getContentRegex.exec(line)) !== null) {
      const [fullMatch, , page, , section, , key, , fallbackText] = match
      const matchStart = match.index

      // Check if the search text matches the fallback text exactly
      if (fallbackText === searchText) {
        // Find the exact position of the fallback text within the getContent call
        const fallbackQuotePattern = new RegExp(`(['"\`])${this.escapeRegex(fallbackText)}\\1`)
        const fallbackMatch = line.substring(matchStart).match(fallbackQuotePattern)

        if (fallbackMatch) {
          const fallbackStart = matchStart + line.substring(matchStart).indexOf(fallbackMatch[0])
          matches.push({
            filePath,
            lineNumber,
            columnStart: fallbackStart + 2, // +1 for quote, +1 for 1-based indexing
            columnEnd: fallbackStart + fallbackText.length + 2,
            fullLine: line,
            textContent: searchText,
            context: 'getContent-fallback',
            confidence: 95 // Very high confidence for getContent matches
          })
        }
      }

      // Check if the search text is part of the fallback text (partial match)
      else if (fallbackText.includes(searchText) && searchText.length >= 3) {
        const fallbackQuotePattern = new RegExp(`(['"\`])${this.escapeRegex(fallbackText)}\\1`)
        const fallbackMatch = line.substring(matchStart).match(fallbackQuotePattern)

        if (fallbackMatch) {
          const fallbackStart = matchStart + line.substring(matchStart).indexOf(fallbackMatch[0])
          const textStartInFallback = fallbackText.indexOf(searchText)

          matches.push({
            filePath,
            lineNumber,
            columnStart: fallbackStart + 1 + textStartInFallback + 1, // +1 for quote, +1 for position, +1 for 1-based
            columnEnd: fallbackStart + 1 + textStartInFallback + searchText.length + 1,
            fullLine: line,
            textContent: searchText,
            context: 'getContent-fallback',
            confidence: 90 // High confidence for partial getContent matches
          })
        }
      }

      // Check if the search text matches content keys (title, subtitle, description, etc.)
      const contentKeys = ['title', 'subtitle', 'title_highlight', 'description', 'button_text', 'label']
      if (contentKeys.some(k => k === key && (searchText.toLowerCase().includes(k) || k.includes(searchText.toLowerCase())))) {
        const keyQuotePattern = new RegExp(`(['"\`])${this.escapeRegex(key)}\\1`)
        const keyMatch = line.substring(matchStart).match(keyQuotePattern)

        if (keyMatch) {
          const keyStart = matchStart + line.substring(matchStart).indexOf(keyMatch[0])
          matches.push({
            filePath,
            lineNumber,
            columnStart: keyStart + 2, // +1 for quote, +1 for 1-based indexing
            columnEnd: keyStart + key.length + 2,
            fullLine: line,
            textContent: key,
            context: 'getContent-key',
            confidence: 85 // High confidence for getContent key matches
          })
        }
      }
    }

    return matches
  }

  /**
   * Escape special regex characters
   */
  private static escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  /**
   * Determine the context of the text (JSX, string literal, etc.)
   */
  private static determineContext(line: string, textIndex: number, searchText: string): TsxTextLocation['context'] {
    const beforeText = line.substring(0, textIndex)
    const afterText = line.substring(textIndex + searchText.length)
    
    // Check for comments
    if (beforeText.includes('//') || beforeText.includes('/*') || afterText.includes('*/')) {
      return 'comment'
    }
    
    // Check for string literals
    const beforeQuotes = (beforeText.match(/"/g) || []).length
    const beforeSingleQuotes = (beforeText.match(/'/g) || []).length
    const beforeBackticks = (beforeText.match(/`/g) || []).length
    
    if (beforeQuotes % 2 === 1 || beforeSingleQuotes % 2 === 1) {
      return 'string-literal'
    }
    
    if (beforeBackticks % 2 === 1) {
      return 'template-literal'
    }
    
    // Check for JSX attributes
    if (this.isInJsxAttribute(line, textIndex)) {
      return 'jsx-attribute'
    }
    
    // Check for JSX text content
    if (this.isInJsxText(line, textIndex)) {
      return 'jsx-text'
    }
    
    return 'unknown'
  }
  
  /**
   * Check if text is inside JSX attribute
   */
  private static isInJsxAttribute(line: string, textIndex: number): boolean {
    const beforeText = line.substring(0, textIndex)
    
    // Look for attribute patterns like className="..." or title="..."
    const attributePattern = /\w+\s*=\s*["'`]?[^"'`]*$/
    return attributePattern.test(beforeText)
  }
  
  /**
   * Check if text is JSX text content (between tags)
   */
  private static isInJsxText(line: string, textIndex: number): boolean {
    const beforeText = line.substring(0, textIndex)
    const afterText = line.substring(textIndex)
    
    // Simple heuristic: if we're between > and < characters, likely JSX text
    const lastOpenTag = beforeText.lastIndexOf('>')
    const lastCloseTag = beforeText.lastIndexOf('<')
    const nextOpenTag = afterText.indexOf('<')
    const nextCloseTag = afterText.indexOf('>')
    
    // We're in JSX text if the last character before us was > and next is <
    return lastOpenTag > lastCloseTag && (nextOpenTag !== -1 || nextCloseTag === -1)
  }
  
  /**
   * Calculate confidence score for a text match
   */
  private static calculateConfidence(line: string, textIndex: number, searchText: string, context: TsxTextLocation['context']): number {
    let confidence = 50 // Base confidence

    // Context-based confidence adjustments
    switch (context) {
      case 'getContent-fallback':
        confidence += 45 // Highest confidence for getContent fallback text
        break
      case 'getContent-key':
        confidence += 35 // Very high confidence for getContent keys
        break
      case 'jsx-text':
        confidence += 30 // High confidence for JSX text
        break
      case 'jsx-attribute':
        confidence += 20 // Good confidence for attributes
        break
      case 'string-literal':
        confidence += 15 // Moderate confidence for strings
        break
      case 'template-literal':
        confidence += 10 // Lower confidence for template literals
        break
      case 'comment':
        confidence -= 20 // Lower confidence for comments
        break
      default:
        confidence += 5
    }
    
    // Boost confidence for exact word matches
    const beforeChar = textIndex > 0 ? line[textIndex - 1] : ' '
    const afterChar = textIndex + searchText.length < line.length ? line[textIndex + searchText.length] : ' '
    
    if (/\s/.test(beforeChar) && /\s/.test(afterChar)) {
      confidence += 15 // Exact word match
    }
    
    // Boost confidence for common UI text patterns
    if (this.isLikelyUIText(searchText)) {
      confidence += 10
    }
    
    // Reduce confidence for very short text
    if (searchText.length < 3) {
      confidence -= 10
    }
    
    // Boost confidence for longer, more specific text
    if (searchText.length > 20) {
      confidence += 10
    }
    
    return Math.max(0, Math.min(100, confidence))
  }
  
  /**
   * Check if text looks like UI text (buttons, headings, etc.)
   */
  private static isLikelyUIText(text: string): boolean {
    const uiPatterns = [
      /^(Get Started|Learn More|Contact Us|About Us|Our Services|View All|Read More)$/i,
      /^(Home|About|Services|Contact|Blog|Portfolio|Team|Projects)$/i,
      /\b(button|link|heading|title|description|label)\b/i
    ]
    
    return uiPatterns.some(pattern => pattern.test(text))
  }
  
  /**
   * Find the best match for text in a TSX file
   */
  static async findBestTextMatch(filePath: string, searchText: string): Promise<TsxTextLocation | null> {
    const locations = await this.findTextInTsxFile(filePath, searchText)
    
    if (locations.length === 0) {
      return null
    }
    
    // Return the highest confidence match
    return locations[0]
  }
  
  /**
   * Search for text across multiple TSX files with intelligent ranking
   */
  static async searchTextInTsxFiles(searchText: string, potentialFiles: string[]): Promise<TsxTextLocation[]> {
    const allLocations: TsxTextLocation[] = []
    
    for (const filePath of potentialFiles) {
      try {
        const locations = await this.findTextInTsxFile(filePath, searchText)
        allLocations.push(...locations)
      } catch (error) {
        // File doesn't exist or can't be read, continue
        continue
      }
    }
    
    // Sort by confidence and return top matches
    return allLocations.sort((a, b) => b.confidence - a.confidence)
  }
}
