/**
 * Utility functions for URL handling
 */

/**
 * Ensures a URL is absolute by adding the current origin if it's relative
 * @param url - The URL to make absolute
 * @param baseUrl - Optional base URL (defaults to current window location)
 * @returns Absolute URL
 */
export function makeAbsoluteUrl(url: string | null | undefined, baseUrl?: string): string | null {
  if (!url) return null
  
  // If it's already an absolute URL (starts with http:// or https://), return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  
  // If it's a relative URL starting with /, make it absolute
  if (url.startsWith('/')) {
    if (typeof window !== 'undefined') {
      return `${window.location.origin}${url}`
    } else if (baseUrl) {
      return `${baseUrl}${url}`
    } else {
      // Fallback for server-side rendering
      return url
    }
  }
  
  // If it's a relative URL not starting with /, return as is
  return url
}

/**
 * React hook to get absolute URL
 * @param url - The URL to make absolute
 * @returns Absolute URL or null
 */
export function useAbsoluteUrl(url: string | null | undefined): string | null {
  return makeAbsoluteUrl(url)
}
