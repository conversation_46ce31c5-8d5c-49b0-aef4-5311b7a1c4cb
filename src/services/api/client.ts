import { prisma } from '@/config/prisma'
import { transformFromDbFields } from '@/lib/utils/data-transform'

export interface ClientWithCounts {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  logoUrl?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    payments: number
  }
}

export interface ClientDetails extends ClientWithCounts {
  projects: Array<{
    id: string | number
    name: string
    status?: string
    projStartDate?: string
    projCompletionDate?: string
    estimateCost?: number
  }>
  contracts: Array<{
    id: string | number
    contName: string
    contStatus?: string
    contValue?: number
    contSignedDate?: string
    contExpiryDate?: string
  }>
  invoices: Array<{
    id: string | number
    totalAmount: number
    status: string
    dueDate: string
    description?: string
    createdAt: string
  }>
}

/**
 * Fetch a single client with detailed information including related data
 */
export async function getClientDetails(clientId: string | number): Promise<ClientDetails | null> {
  try {
    const client = await prisma.clients.findUnique({
      where: { id: Number(clientId) },
      include: {
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
            projstartdate: true,
            projcompletiondate: true,
            estimatecost: true,
          },
          orderBy: {
            createdat: 'desc',
          },
        },
        contracts: {
          select: {
            id: true,
            contname: true,
            contstatus: true,
            contvalue: true,
            contsigneddate: true,
            contexpirydate: true,
          },
          orderBy: {
            createdat: 'desc',
          },
        },
        invoices: {
          select: {
            id: true,
            totalamount: true,
            status: true,
            duedate: true,
            description: true,
            createdat: true,
          },
          orderBy: {
            createdat: 'desc',
          },
        },
        _count: {
          select: {
            projects: true,
            contracts: true,
            invoices: true,
            payments: {
              where: {
                invoices: {
                  clientid: Number(clientId)
                }
              }
            },
          },
        },
      },
    })

    if (!client) {
      return null
    }

    // Transform the data using existing transformation functions
    const transformedClient = transformFromDbFields.client(client)
    
    return {
      ...transformedClient,
      projects: client.projects.map(project => ({
        id: project.id.toString(),
        name: project.name,
        status: project.status || undefined,
        projStartDate: project.projstartdate?.toISOString(),
        projCompletionDate: project.projcompletiondate?.toISOString(),
        estimateCost: project.estimatecost || undefined,
      })),
      contracts: client.contracts.map(contract => ({
        id: contract.id.toString(),
        contName: contract.contname,
        contStatus: contract.contstatus || undefined,
        contValue: contract.contvalue || undefined,
        contSignedDate: contract.contsigneddate?.toISOString(),
        contExpiryDate: contract.contexpirydate?.toISOString(),
      })),
      invoices: client.invoices.map(invoice => ({
        id: invoice.id.toString(),
        totalAmount: Number(invoice.totalamount),
        status: invoice.status,
        dueDate: invoice.duedate.toISOString(),
        description: invoice.description || undefined,
        createdAt: invoice.createdat.toISOString(),
      })),
    }
  } catch (error) {
    console.error('Error fetching client details:', error)
    throw new Error('Failed to fetch client details')
  }
}

/**
 * Fetch basic client information
 */
export async function getClient(clientId: string | number): Promise<ClientWithCounts | null> {
  try {
    const client = await prisma.clients.findUnique({
      where: { id: Number(clientId) },
      include: {
        _count: {
          select: {
            projects: true,
            contracts: true,
            invoices: true,
            payments: {
              where: {
                invoices: {
                  clientid: Number(clientId)
                }
              }
            },
          },
        },
      },
    })

    if (!client) {
      return null
    }

    return transformFromDbFields.client(client)
  } catch (error) {
    console.error('Error fetching client:', error)
    throw new Error('Failed to fetch client')
  }
}

/**
 * Check if a client exists
 */
export async function clientExists(clientId: string | number): Promise<boolean> {
  try {
    const client = await prisma.clients.findUnique({
      where: { id: Number(clientId) },
      select: { id: true },
    })
    return !!client
  } catch (error) {
    console.error('Error checking client existence:', error)
    return false
  }
}
