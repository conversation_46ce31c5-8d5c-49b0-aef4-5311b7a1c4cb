import { prisma } from '@/config/prisma'

export interface InvoiceListItem {
  id: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
  contract?: {
    id: string | number
    contName: string
  }
  project?: {
    id: string | number
    name: string
  }
  order?: {
    id: string | number
    orderTitle: string
  }
}

export interface InvoiceDetails extends InvoiceListItem {
  items: Array<{
    id: string | number
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
    createdAt: string
  }>
  payments: Array<{
    id: string | number
    amount: number
    paymentDate: string
    paymentMethod: string
    status: string
    notes?: string
    createdAt: string
  }>
}

/**
 * Fetch invoices for a specific client
 */
export async function getClientInvoices(
  clientId: string | number,
  options: {
    page?: number
    limit?: number
    search?: string
    status?: string
    dateFrom?: string
    dateTo?: string
  } = {}
): Promise<{ invoices: InvoiceListItem[]; total: number }> {
  try {
    const { page = 1, limit = 10, search, status, dateFrom, dateTo } = options
    const skip = (page - 1) * limit

    const where: any = {
      clientid: Number(clientId),
    }

    if (search) {
      where.OR = [
        { description: { contains: search, mode: 'insensitive' } },
        { contracts: { contname: { contains: search, mode: 'insensitive' } } },
        { projects: { name: { contains: search, mode: 'insensitive' } } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (dateFrom || dateTo) {
      where.duedate = {}
      if (dateFrom) {
        where.duedate.gte = new Date(dateFrom)
      }
      if (dateTo) {
        where.duedate.lte = new Date(dateTo)
      }
    }

    const [invoices, total] = await Promise.all([
      prisma.invoices.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdat: 'desc',
        },
        include: {
          contracts: {
            select: {
              id: true,
              contname: true,
            },
          },
          projects: {
            select: {
              id: true,
              name: true,
            },
          },
          orders: {
            select: {
              id: true,
              ordertitle: true,
            },
          },
        },
      }),
      prisma.invoices.count({ where }),
    ])

    return {
      invoices: invoices.map(invoice => ({
        id: invoice.id.toString(),
        totalAmount: Number(invoice.totalamount),
        subtotal: invoice.subtotal ? Number(invoice.subtotal) : undefined,
        taxRate: Number(invoice.taxrate),
        taxAmount: Number(invoice.taxamount),
        status: invoice.status,
        dueDate: invoice.duedate.toISOString(),
        description: invoice.description || undefined,
        paidAt: invoice.paidat?.toISOString(),
        createdAt: invoice.createdat.toISOString(),
        updatedAt: invoice.updatedat?.toISOString(),
        contract: invoice.contracts ? {
          id: invoice.contracts.id.toString(),
          contName: invoice.contracts.contname,
        } : undefined,
        project: invoice.projects ? {
          id: invoice.projects.id.toString(),
          name: invoice.projects.name,
        } : undefined,
        order: invoice.orders ? {
          id: invoice.orders.id.toString(),
          orderTitle: invoice.orders.ordertitle,
        } : undefined,
      })),
      total,
    }
  } catch (error) {
    console.error('Error fetching client invoices:', error)
    throw new Error('Failed to fetch client invoices')
  }
}

/**
 * Fetch detailed information for a specific invoice
 */
export async function getInvoiceDetails(invoiceId: string | number): Promise<InvoiceDetails | null> {
  try {
    const invoice = await prisma.invoices.findUnique({
      where: { id: Number(invoiceId) },
      include: {
        contracts: {
          select: {
            id: true,
            contname: true,
          },
        },
        projects: {
          select: {
            id: true,
            name: true,
          },
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
          },
        },
        invoiceitems: {
          orderBy: {
            createdat: 'asc',
          },
        },
        payments: {
          orderBy: {
            createdat: 'desc',
          },
        },
      },
    })

    if (!invoice) {
      return null
    }

    return {
      id: invoice.id.toString(),
      totalAmount: Number(invoice.totalamount),
      subtotal: invoice.subtotal ? Number(invoice.subtotal) : undefined,
      taxRate: Number(invoice.taxrate),
      taxAmount: Number(invoice.taxamount),
      status: invoice.status,
      dueDate: invoice.duedate.toISOString(),
      description: invoice.description || undefined,
      paidAt: invoice.paidat?.toISOString(),
      createdAt: invoice.createdat.toISOString(),
      updatedAt: invoice.updatedat?.toISOString(),
      contract: invoice.contracts ? {
        id: invoice.contracts.id.toString(),
        contName: invoice.contracts.contname,
      } : undefined,
      project: invoice.projects ? {
        id: invoice.projects.id.toString(),
        name: invoice.projects.name,
      } : undefined,
      order: invoice.orders ? {
        id: invoice.orders.id.toString(),
        orderTitle: invoice.orders.ordertitle,
      } : undefined,
      items: invoice.invoiceitems.map(item => ({
        id: item.id.toString(),
        description: item.description,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitprice),
        totalPrice: Number(item.totalprice),
        createdAt: item.createdat.toISOString(),
      })),
      payments: invoice.payments.map(payment => ({
        id: payment.id.toString(),
        amount: Number(payment.amount),
        paymentDate: payment.paymentdate.toISOString(),
        paymentMethod: payment.paymentmethod,
        status: payment.status,
        notes: payment.notes || undefined,
        createdAt: payment.createdat.toISOString(),
      })),
    }
  } catch (error) {
    console.error('Error fetching invoice details:', error)
    throw new Error('Failed to fetch invoice details')
  }
}

/**
 * Get invoice status options
 */
export function getInvoiceStatusOptions() {
  return [
    { value: 'Pending', label: 'Pending' },
    { value: 'Sent', label: 'Sent' },
    { value: 'Paid', label: 'Paid' },
    { value: 'Overdue', label: 'Overdue' },
    { value: 'Cancelled', label: 'Cancelled' },
  ]
}

/**
 * Calculate invoice totals and statistics for a client
 */
export async function getClientInvoiceStats(clientId: string | number) {
  try {
    const stats = await prisma.invoices.aggregate({
      where: {
        clientid: Number(clientId),
      },
      _sum: {
        totalamount: true,
        taxamount: true,
      },
      _count: {
        id: true,
      },
    })

    const statusCounts = await prisma.invoices.groupBy({
      by: ['status'],
      where: {
        clientid: Number(clientId),
      },
      _count: {
        id: true,
      },
    })

    return {
      totalAmount: Number(stats._sum.totalamount || 0),
      totalTax: Number(stats._sum.taxamount || 0),
      totalInvoices: stats._count.id,
      statusBreakdown: statusCounts.reduce((acc, item) => {
        acc[item.status] = item._count.id
        return acc
      }, {} as Record<string, number>),
    }
  } catch (error) {
    console.error('Error fetching client invoice stats:', error)
    throw new Error('Failed to fetch client invoice stats')
  }
}
