import { NextRequest } from 'next/server'
import crypto from 'crypto'

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32
const CSRF_TOKEN_HEADER = 'x-csrf-token'
const CSRF_TOKEN_COOKIE = 'csrf-token'

// In-memory store for CSRF tokens (in production, use Redis or database)
const csrfTokenStore = new Map<string, { token: string; expires: number }>()

// Clean up expired tokens every hour
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of csrfTokenStore.entries()) {
    if (value.expires < now) {
      csrfTokenStore.delete(key)
    }
  }
}, 60 * 60 * 1000)

export class CSRFProtection {
  /**
   * Generate a new CSRF token
   */
  static generateToken(): string {
    return crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex')
  }

  /**
   * Create a CSRF token for a session
   */
  static createToken(sessionId: string): string {
    const token = this.generateToken()
    const expires = Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    
    csrfTokenStore.set(sessionId, { token, expires })
    
    return token
  }

  /**
   * Validate CSRF token from request
   */
  static validateToken(request: NextRequest, sessionId: string): boolean {
    try {
      // Get token from header
      const headerToken = request.headers.get(CSRF_TOKEN_HEADER)
      
      // Get token from cookie as fallback
      const cookieToken = request.cookies.get(CSRF_TOKEN_COOKIE)?.value
      
      const providedToken = headerToken || cookieToken
      
      if (!providedToken) {
        return false
      }

      // Get stored token
      const storedTokenData = csrfTokenStore.get(sessionId)
      
      if (!storedTokenData) {
        return false
      }

      // Check if token is expired
      if (storedTokenData.expires < Date.now()) {
        csrfTokenStore.delete(sessionId)
        return false
      }

      // Compare tokens using constant-time comparison
      return crypto.timingSafeEqual(
        Buffer.from(providedToken, 'hex'),
        Buffer.from(storedTokenData.token, 'hex')
      )
    } catch (error) {
      console.error('CSRF token validation error:', error)
      return false
    }
  }

  /**
   * Get CSRF token for a session
   */
  static getToken(sessionId: string): string | null {
    const tokenData = csrfTokenStore.get(sessionId)
    
    if (!tokenData || tokenData.expires < Date.now()) {
      return null
    }
    
    return tokenData.token
  }

  /**
   * Remove CSRF token for a session
   */
  static removeToken(sessionId: string): void {
    csrfTokenStore.delete(sessionId)
  }

  /**
   * Middleware to check CSRF token
   */
  static async checkCSRF(request: NextRequest, sessionId: string): Promise<Response | null> {
    // Skip CSRF check for GET, HEAD, OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
      return null
    }

    // Skip CSRF check for same-origin requests (optional)
    const origin = request.headers.get('origin')
    const host = request.headers.get('host')
    
    if (origin && host && new URL(origin).host === host) {
      // Same origin, but still validate token if provided
      if (request.headers.get(CSRF_TOKEN_HEADER) || request.cookies.get(CSRF_TOKEN_COOKIE)) {
        if (!this.validateToken(request, sessionId)) {
          return new Response(
            JSON.stringify({
              error: 'Invalid CSRF token',
              message: 'CSRF token validation failed'
            }),
            {
              status: 403,
              headers: { 'Content-Type': 'application/json' }
            }
          )
        }
      }
      return null
    }

    // Cross-origin request, CSRF token required
    if (!this.validateToken(request, sessionId)) {
      return new Response(
        JSON.stringify({
          error: 'CSRF token required',
          message: 'Valid CSRF token is required for this request'
        }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    return null
  }
}

// Helper function to get session ID from request
export function getSessionId(request: NextRequest): string {
  // This should extract session ID from JWT token or session cookie
  // For now, we'll use a combination of IP and User-Agent as a simple identifier
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwarded?.split(',')[0] || realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || ''
  
  return crypto
    .createHash('sha256')
    .update(`${ip}:${userAgent}`)
    .digest('hex')
    .slice(0, 16)
}

// Constants for use in components
export const CSRF_HEADER_NAME = CSRF_TOKEN_HEADER
export const CSRF_COOKIE_NAME = CSRF_TOKEN_COOKIE
