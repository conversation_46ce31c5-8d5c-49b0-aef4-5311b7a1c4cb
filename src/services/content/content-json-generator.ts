import { getPublicPages, type PublicPage, type PageSection } from '@/lib/utils/page-detector'
import { prisma } from '@/config/prisma'

export interface ContentJSON {
  pages: {
    [pageId: string]: {
      name: string
      sections: {
        [sectionId: string]: {
          name: string
          fields: {
            [fieldKey: string]: {
              label: string
              type: string
              value: string
              description?: string
            }
          }
          slides?: {
            [slideId: string]: {
              title: string
              subtitle: string
              description: string
              buttonText: string
              buttonLink: string
              imageUrl: string
              displayOrder: number
              isActive: boolean
            }
          }
        }
      }
    }
  }
  metadata: {
    generatedAt: string
    totalPages: number
    totalSections: number
    totalFields: number
    totalSlides: number
  }
}

export async function generateContentJSON(): Promise<ContentJSON> {
  const pages = getPublicPages()
  const contentJSON: ContentJSON = {
    pages: {},
    metadata: {
      generatedAt: new Date().toISOString(),
      totalPages: 0,
      totalSections: 0,
      totalFields: 0,
      totalSlides: 0
    }
  }

  // Fetch all static content from database
  const staticContent = await prisma.staticcontent.findMany({
    where: { isactive: true },
    orderBy: [
      { page: 'asc' },
      { section: 'asc' },
      { displayorder: 'asc' }
    ]
  })

  // Group content by page and section
  const contentByPage: { [key: string]: { [key: string]: any[] } } = {}
  staticContent.forEach(item => {
    if (!contentByPage[item.page]) {
      contentByPage[item.page] = {}
    }
    if (!contentByPage[item.page][item.section]) {
      contentByPage[item.page][item.section] = []
    }
    contentByPage[item.page][item.section].push(item)
  })

  // Process each page
  for (const page of pages) {
    contentJSON.pages[page.id] = {
      name: page.name,
      sections: {}
    }

    // Process each section
    for (const section of page.sections) {
      contentJSON.pages[page.id].sections[section.id] = {
        name: section.title,
        fields: {}
      }

      // Process fields
      for (const [fieldKey, fieldValue] of Object.entries(section.fields)) {
        const dbContent = contentByPage[page.id]?.[section.id]?.find(
          item => item.contentkey === fieldKey
        )

        contentJSON.pages[page.id].sections[section.id].fields[fieldKey] = {
          label: fieldKey,
          type: 'text',
          value: dbContent?.content || fieldValue || '',
          description: `Field for ${section.title}`
        }

        contentJSON.metadata.totalFields++
      }

      // Process hero slides if this is a hero section
      if (section.type === 'hero' && section.slides) {
        contentJSON.pages[page.id].sections[section.id].slides = {}

        // Get slides from database
        const slideContent = contentByPage[page.id]?.[section.id]?.filter(
          item => item.contenttype === 'hero_slide'
        ) || []

        for (const slide of section.slides) {
          const dbSlide = slideContent.find(item => item.contentkey === slide.id)
          let slideData = slide

          if (dbSlide && dbSlide.content) {
            try {
              slideData = { ...slide, ...JSON.parse(dbSlide.content) }
            } catch (e) {
              console.warn(`Failed to parse slide content for ${slide.id}`)
            }
          }

          contentJSON.pages[page.id].sections[section.id].slides![slide.id] = {
            title: slideData.title || '',
            subtitle: slideData.subtitle || '',
            description: slideData.subtitle || '',
            buttonText: slideData.buttonText || '',
            buttonLink: slideData.buttonUrl || '',
            imageUrl: slideData.imageUrl || '',
            displayOrder: slideData.displayOrder || 1,
            isActive: slideData.isActive !== false
          }

          contentJSON.metadata.totalSlides++
        }
      }

      contentJSON.metadata.totalSections++
    }

    contentJSON.metadata.totalPages++
  }

  return contentJSON
}

export async function applyContentJSON(contentJSON: ContentJSON): Promise<{
  success: boolean
  message: string
  stats: {
    pagesUpdated: number
    fieldsUpdated: number
    slidesUpdated: number
  }
}> {
  try {
    let pagesUpdated = 0
    let fieldsUpdated = 0
    let slidesUpdated = 0

    // Process each page
    for (const [pageId, pageData] of Object.entries(contentJSON.pages)) {
      // Process each section
      for (const [sectionId, sectionData] of Object.entries(pageData.sections)) {
        // Process fields
        for (const [fieldKey, fieldData] of Object.entries(sectionData.fields)) {
          try {
            // Check if content exists
            const existingContent = await prisma.staticcontent.findFirst({
              where: {
                page: pageId,
                section: sectionId,
                contentkey: fieldKey,
                isactive: true
              }
            })

            if (existingContent) {
              // Update existing content
              await prisma.staticcontent.update({
                where: { id: existingContent.id },
                data: {
                  content: fieldData.value,
                  updatedat: new Date()
                }
              })
            } else {
              // Create new content
              await prisma.staticcontent.create({
                data: {
                  page: pageId,
                  section: sectionId,
                  contentkey: fieldKey,
                  contenttype: fieldData.type || 'text',
                  content: fieldData.value,
                  displayorder: 1,
                  isactive: true,
                  createdat: new Date(),
                  updatedat: new Date()
                }
              })
            }
            fieldsUpdated++
          } catch (fieldError) {
            console.error(`Error processing field ${fieldKey}:`, fieldError)
            // Continue with other fields
          }
        }

        // Process slides if they exist
        if (sectionData.slides) {
          try {
            // First, deactivate all existing slides for this section
            const existingSlides = await prisma.staticcontent.findMany({
              where: {
                page: pageId,
                section: sectionId,
                contenttype: 'hero_slide',
                isactive: true
              }
            })

            for (const slide of existingSlides) {
              await prisma.staticcontent.update({
                where: { id: slide.id },
                data: { isactive: false }
              })
            }

                      // Create/update slides
          for (const [slideId, slideData] of Object.entries(sectionData.slides)) {
            await prisma.staticcontent.upsert({
              where: {
                page_section_contentkey: {
                  page: pageId,
                  section: sectionId,
                  contentkey: slideId
                }
              },
              update: {
                content: JSON.stringify(slideData),
                displayorder: slideData.displayOrder || 1,
                isactive: slideData.isActive !== false,
                updatedat: new Date()
              },
              create: {
                page: pageId,
                section: sectionId,
                contentkey: slideId,
                contenttype: 'hero_slide',
                content: JSON.stringify(slideData),
                displayorder: slideData.displayOrder || 1,
                isactive: slideData.isActive !== false,
                createdat: new Date(),
                updatedat: new Date()
              }
            })
            slidesUpdated++
          }
          } catch (slideError) {
            console.error(`Error processing slides for section ${sectionId}:`, slideError)
            // Continue with other sections
          }
        }
      }
      pagesUpdated++
    }

    return {
      success: true,
      message: 'Content JSON applied successfully',
      stats: {
        pagesUpdated,
        fieldsUpdated,
        slidesUpdated
      }
    }
  } catch (error) {
    console.error('Error applying content JSON:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to apply content JSON',
      stats: {
        pagesUpdated: 0,
        fieldsUpdated: 0,
        slidesUpdated: 0
      }
    }
  }
} 