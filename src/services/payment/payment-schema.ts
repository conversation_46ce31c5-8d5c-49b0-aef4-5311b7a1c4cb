import { z } from 'zod'

export const paymentMethodSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('stripe_card'),
    cardNumber: z.string().min(1, 'Card number is required'),
    expiryDate: z.string().min(1, 'Expiry date is required'),
    cvv: z.string().min(3, 'CVV must be at least 3 digits'),
    cardholderName: z.string().min(1, 'Cardholder name is required'),
  }),
  z.object({
    type: z.literal('paypal'),
    paypalEmail: z.string().email('Valid PayPal email is required').optional(),
  }),
  z.object({
    type: z.literal('bank_transfer'),
    bankName: z.string().min(1, 'Bank name is required'),
    accountNumber: z.string().min(1, 'Account number is required'),
    routingNumber: z.string().min(1, 'Routing number is required'),
    accountHolderName: z.string().min(1, 'Account holder name is required'),
  }),
  z.object({
    type: z.literal('wire_transfer'),
    bankName: z.string().min(1, 'Bank name is required'),
    swiftCode: z.string().min(1, 'SWIFT code is required'),
    accountNumber: z.string().min(1, 'Account number is required'),
    accountHolderName: z.string().min(1, 'Account holder name is required'),
    bankAddress: z.string().min(1, 'Bank address is required'),
  }),
  z.object({
    type: z.literal('ach_transfer'),
    bankName: z.string().min(1, 'Bank name is required'),
    accountNumber: z.string().min(1, 'Account number is required'),
    routingNumber: z.string().min(1, 'Routing number is required'),
    accountHolderName: z.string().min(1, 'Account holder name is required'),
  }),
  z.object({
    type: z.literal('apple_pay'),
  }),
  z.object({
    type: z.literal('google_pay'),
  }),
  z.object({
    type: z.literal('check'),
    checkNumber: z.string().min(1, 'Check number is required'),
    bankName: z.string().min(1, 'Bank name is required'),
  }),
  z.object({
    type: z.literal('cash'),
  }),
])

export const paymentFormSchema = z.object({
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(1, 'Currency is required'),
  paymentMethod: paymentMethodSchema,
  paymentDate: z.string().min(1, 'Payment date is required'),
  notes: z.string().optional(),
  promoCode: z.string().optional(),
  emailReceipt: z.boolean().default(false),
  receiptEmail: z.string().email('Valid email is required').optional(),
  termsAccepted: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions',
  }),
})

export type PaymentFormData = z.infer<typeof paymentFormSchema>
export type PaymentMethodData = z.infer<typeof paymentMethodSchema>

export const PROMO_CODES = [
  { code: 'SAVE10', discount: 10, type: 'percentage' as const },
  { code: 'WELCOME20', discount: 20, type: 'percentage' as const },
  { code: 'FIRST50', discount: 50, type: 'fixed' as const },
  { code: 'LOYAL100', discount: 100, type: 'fixed' as const },
]
