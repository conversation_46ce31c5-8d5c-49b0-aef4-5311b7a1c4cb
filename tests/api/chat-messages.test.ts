/**
 * @jest-environment node
 */

// External dependencies
import { createMocks } from 'node-mocks-http'
import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'

// Internal dependencies
import { GET, POST } from '@/app/api/admin/contact-forms/[id]/messages/route'
import { GET as ClientGET, POST as ClientPOST } from '@/app/api/chat/contact-forms/[id]/messages/route'

// Mock dependencies
jest.mock('@/config/prisma', () => ({
  prisma: {
    contactforms: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    users: {
      findFirst: jest.fn(),
    },
  },
}))

jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

jest.mock('@/services/auth/auth-config', () => ({
  authOptions: {},
}))

// Import mocked dependencies after mocking
import { prisma } from '@/config/prisma'

// Type definitions
interface MockUser {
  id: string
  email: string
  role: string
  firstname?: string
  lastname?: string
}

interface MockContactForm {
  id: bigint
  threadid: bigint
  subject: string
  senderid?: bigint
  receiverid?: bigint
}

interface MockMessage {
  id: bigint
  subject: string
  message: string
  createdat: Date
  senderid?: bigint
  receiverid?: bigint
  sender?: { id: number; email: string; role: string }
  receiver?: { id: number; email: string; role: string }
  parent: null
  attachments: null
}

// Mock instances
const mockPrisma = prisma as jest.Mocked<typeof prisma>
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>

// Test utilities
const createMockRequest = (method: 'GET' | 'POST', url: string, body?: any): NextRequest => {
  const { req } = createMocks({ method, url, body })
  return new NextRequest(`http://localhost:3000${url}`, {
    method: req.method as 'GET' | 'POST',
    headers: body ? { 'content-type': 'application/json' } : req.headers as any,
    body: body ? JSON.stringify(body) : undefined,
  })
}

const createMockParams = (id: string) => Promise.resolve({ id })

// Test data factories
const createMockUser = (overrides: Partial<MockUser> = {}): MockUser => ({
  id: '1',
  email: '<EMAIL>',
  role: 'USER',
  ...overrides,
})

const createMockContactForm = (overrides: Partial<MockContactForm> = {}): MockContactForm => ({
  id: BigInt(1),
  threadid: BigInt(1),
  subject: 'Test Subject',
  ...overrides,
})

const createMockMessage = (overrides: Partial<MockMessage> = {}): MockMessage => ({
  id: BigInt(1),
  subject: 'Test Subject',
  message: 'Test message',
  createdat: new Date(),
  sender: { id: 1, email: '<EMAIL>', role: 'USER' },
  receiver: { id: 2, email: '<EMAIL>', role: 'ADMIN' },
  parent: null,
  attachments: null,
  ...overrides,
})

describe('Chat Messages API', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('/api/admin/contact-forms/[id]/messages', () => {
    describe('GET - Admin Access', () => {
      it('should return messages for admin users', async () => {
        // Arrange
        const adminUser = createMockUser({ id: '1', email: '<EMAIL>', role: 'ADMIN' })
        const contactForm = createMockContactForm()
        const messages = [createMockMessage()]

        mockGetServerSession.mockResolvedValue({ user: adminUser } as any)
        ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(contactForm as any)
        ;(mockPrisma.contactforms.findMany as jest.Mock).mockResolvedValue(messages as any)

        const request = createMockRequest('GET', '/api/admin/contact-forms/1/messages')

        // Act
        const response = await GET(request, { params: createMockParams('1') })
        const data = await response.json()

        // Assert
        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.messages).toHaveLength(1)
        expect(data.data.threadId).toBe('1')
      })

      it('should return 404 for non-existent contact form', async () => {
        // Arrange
        const adminUser = createMockUser({ id: '1', email: '<EMAIL>', role: 'ADMIN' })
        
        mockGetServerSession.mockResolvedValue({ user: adminUser } as any)
        ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(null)

        const request = createMockRequest('GET', '/api/admin/contact-forms/999/messages')

        // Act
        const response = await GET(request, { params: createMockParams('999') })
        const data = await response.json()

        // Assert
        expect(response.status).toBe(404)
        expect(data.success).toBe(false)
      })
    })

    describe('POST - Admin Access', () => {
      it('should create a new message for admin users', async () => {
        // Arrange
        const adminUser = createMockUser({ id: '1', email: '<EMAIL>', role: 'ADMIN' })
        const contactForm = createMockContactForm({ senderid: BigInt(2) })
        const newMessage = createMockMessage({
          id: BigInt(2),
          subject: 'Re: Test Subject',
          message: 'Reply message',
          sender: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
          receiver: { id: 2, email: '<EMAIL>', role: 'USER' },
        })

        mockGetServerSession.mockResolvedValue({ user: adminUser } as any)
        ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(contactForm as any)
        ;(mockPrisma.contactforms.create as jest.Mock).mockResolvedValue(newMessage as any)

        const requestBody = {
          message: 'Reply message',
          contenttype: 'text',
          attachments: [],
          messagetype: 'chat',
        }
        const request = createMockRequest('POST', '/api/admin/contact-forms/1/messages', requestBody)

        // Act
        const response = await POST(request, { params: createMockParams('1') })
        const data = await response.json()

        // Assert
        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.message.message).toBe('Reply message')
      })
    })
  })

  describe('/api/chat/contact-forms/[id]/messages', () => {
    describe('GET - Client Access', () => {
      it('should return messages for authenticated users', async () => {
        // Arrange
        const user = createMockUser({ id: '2', email: '<EMAIL>', role: 'USER' })
        const contactForm = createMockContactForm({ senderid: BigInt(2) })
        const messages = [createMockMessage({
          senderid: BigInt(2),
          receiverid: BigInt(1),
          sender: { id: 2, email: '<EMAIL>', role: 'USER' },
          receiver: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
        })]

        mockGetServerSession.mockResolvedValue({ user } as any)
        ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(contactForm as any)
        ;(mockPrisma.contactforms.findMany as jest.Mock).mockResolvedValue(messages as any)

        const request = createMockRequest('GET', '/api/chat/contact-forms/1/messages')

        // Act
        const response = await ClientGET(request, { params: createMockParams('1') })
        const data = await response.json()

        // Assert
        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.messages).toHaveLength(1)
      })

      it('should deny access to unauthorized users', async () => {
        // Arrange
        const unauthorizedUser = createMockUser({ id: '3', email: '<EMAIL>', role: 'USER' })
        const contactForm = createMockContactForm({ 
          senderid: BigInt(2), 
          receiverid: BigInt(1) 
        })

        mockGetServerSession.mockResolvedValue({ user: unauthorizedUser } as any)
        ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(contactForm as any)

        const request = createMockRequest('GET', '/api/chat/contact-forms/1/messages')

        // Act
        const response = await ClientGET(request, { params: createMockParams('1') })
        const data = await response.json()

        // Assert
        expect(response.status).toBe(403)
        expect(data.success).toBe(false)
      })
    })

    describe('POST - Client Access', () => {
      it('should create a new message for authenticated users', async () => {
        // Arrange
        const user = createMockUser({ 
          id: '2', 
          email: '<EMAIL>', 
          role: 'USER',
          firstname: 'John',
          lastname: 'Doe'
        })
        const contactForm = createMockContactForm({ senderid: BigInt(2) })
        const newMessage = createMockMessage({
          id: BigInt(2),
          subject: 'Re: Test Subject',
          message: 'User reply',
          sender: { id: 2, email: '<EMAIL>', role: 'USER' },
          receiver: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
        })

        mockGetServerSession.mockResolvedValue({ user } as any)
        ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(contactForm as any)
        ;(mockPrisma.users.findFirst as jest.Mock).mockResolvedValue({ id: 1 } as any)
        ;(mockPrisma.contactforms.create as jest.Mock).mockResolvedValue(newMessage as any)

        const requestBody = {
          message: 'User reply',
          contenttype: 'text',
          attachments: [],
          messagetype: 'chat',
        }
        const request = createMockRequest('POST', '/api/chat/contact-forms/1/messages', requestBody)

        // Act
        const response = await ClientPOST(request, { params: createMockParams('1') })
        const data = await response.json()

        // Assert
        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.message.message).toBe('User reply')
      })
    })
  })
})
