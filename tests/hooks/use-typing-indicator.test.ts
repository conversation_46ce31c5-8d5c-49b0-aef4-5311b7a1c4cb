import { renderHook, act } from '@testing-library/react'
import { useTypingIndicator } from '@/hooks/use-typing-indicator'

describe('useTypingIndicator', () => {
  const defaultOptions = {
    contactFormId: 1,
    currentUserId: 'current-user',
    enabled: true,
    typingTimeout: 3000
  }

  beforeEach(() => {
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should initialize with empty typing users', () => {
    const { result } = renderHook(() => useTypingIndicator(defaultOptions))

    expect(result.current.typingUsers).toEqual([])
    expect(result.current.isCurrentUserTyping).toBe(false)
  })

  it('should start typing and set timeout', () => {
    const { result } = renderHook(() => useTypingIndicator(defaultOptions))

    act(() => {
      result.current.startTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(true)
  })

  it('should stop typing immediately', () => {
    const { result } = renderHook(() => useTypingIndicator(defaultOptions))

    act(() => {
      result.current.startTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(true)

    act(() => {
      result.current.stopTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(false)
  })

  it('should automatically stop typing after timeout', () => {
    const { result } = renderHook(() => useTypingIndicator({ ...defaultOptions, typingTimeout: 1000 }))

    act(() => {
      result.current.startTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(true)

    // Advance time by timeout duration
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    expect(result.current.isCurrentUserTyping).toBe(false)
  })

  it('should handle multiple users typing', () => {
    const { result } = renderHook(() => useTypingIndicator(defaultOptions))

    act(() => {
      result.current.addTypingUser({ id: 'user1', name: 'User 1' })
      result.current.addTypingUser({ id: 'user2', name: 'User 2' })
    })

    expect(result.current.typingUsers).toHaveLength(2)
    expect(result.current.typingUsers.find(u => u.id === 'user1')).toBeDefined()
    expect(result.current.typingUsers.find(u => u.id === 'user2')).toBeDefined()
  })

  it('should not duplicate users in typing list', () => {
    const { result } = renderHook(() => useTypingIndicator(defaultOptions))

    act(() => {
      result.current.addTypingUser({ id: 'user1', name: 'User 1' })
      result.current.addTypingUser({ id: 'user1', name: 'User 1' }) // Duplicate
    })

    expect(result.current.typingUsers).toHaveLength(1)
    expect(result.current.typingUsers[0].id).toBe('user1')
  })

  it('should reset timeout when user starts typing again', () => {
    const { result } = renderHook(() => useTypingIndicator({ ...defaultOptions, typingTimeout: 3000 }))

    act(() => {
      result.current.startTyping()
    })

    // Advance time partially
    act(() => {
      jest.advanceTimersByTime(1500)
    })

    // User starts typing again - should reset timeout
    act(() => {
      result.current.startTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(true)

    // Advance time by full timeout - user should stop typing
    act(() => {
      jest.advanceTimersByTime(3000)
    })

    expect(result.current.isCurrentUserTyping).toBe(false)
  })

  it('should maintain typing state correctly with multiple users', () => {
    const { result } = renderHook(() => useTypingIndicator({ ...defaultOptions, typingTimeout: 3000 }))

    act(() => {
      result.current.addTypingUser({ id: 'user1', name: 'User 1' })
      result.current.addTypingUser({ id: 'user2', name: 'User 2' })
    })

    expect(result.current.typingUsers).toHaveLength(2)

    // Remove one user
    act(() => {
      result.current.removeTypingUser('user1')
    })

    expect(result.current.typingUsers).toHaveLength(1)
    expect(result.current.typingUsers[0].id).toBe('user2')

    // Remove second user
    act(() => {
      result.current.removeTypingUser('user2')
    })

    expect(result.current.typingUsers).toHaveLength(0)
  })

  it('should clear all typing users', () => {
    const { result } = renderHook(() => useTypingIndicator(defaultOptions))

    act(() => {
      result.current.addTypingUser({ id: 'user1', name: 'User 1' })
      result.current.addTypingUser({ id: 'user2', name: 'User 2' })
    })

    expect(result.current.typingUsers).toHaveLength(2)

    act(() => {
      result.current.clearAllTypingUsers()
    })

    expect(result.current.typingUsers).toHaveLength(0)
  })

  it('should handle edge case of removing non-existent user', () => {
    const { result } = renderHook(() => useTypingIndicator(defaultOptions))

    act(() => {
      result.current.addTypingUser({ id: 'user1', name: 'User 1' })
    })

    // Try to remove a user that's not typing
    act(() => {
      result.current.removeTypingUser('user2')
    })

    expect(result.current.typingUsers).toHaveLength(1)
    expect(result.current.typingUsers[0].id).toBe('user1')
  })

  it('should use custom timeout value', () => {
    const customTimeout = 5000
    const { result } = renderHook(() => useTypingIndicator({ ...defaultOptions, typingTimeout: customTimeout }))

    act(() => {
      result.current.startTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(true)

    // Advance time by less than custom timeout
    act(() => {
      jest.advanceTimersByTime(customTimeout - 1000)
    })

    expect(result.current.isCurrentUserTyping).toBe(true)

    // Advance time to complete custom timeout
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    expect(result.current.isCurrentUserTyping).toBe(false)
  })

  it('should clean up timeouts on unmount', () => {
    const { result, unmount } = renderHook(() => useTypingIndicator(defaultOptions))

    act(() => {
      result.current.startTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(true)

    // Unmount the hook
    unmount()

    // Advance time - should not cause any issues
    act(() => {
      jest.advanceTimersByTime(5000)
    })

    // No assertions needed - just ensuring no errors occur
  })

  it('should handle rapid start/stop typing calls', () => {
    const { result } = renderHook(() => useTypingIndicator({ ...defaultOptions, typingTimeout: 1000 }))

    // Rapid start/stop calls
    act(() => {
      result.current.startTyping()
      result.current.stopTyping()
      result.current.startTyping()
      result.current.stopTyping()
      result.current.startTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(true)

    act(() => {
      result.current.stopTyping()
    })

    expect(result.current.isCurrentUserTyping).toBe(false)
  })
})
